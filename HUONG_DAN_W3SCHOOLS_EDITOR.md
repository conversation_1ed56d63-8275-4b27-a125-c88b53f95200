# Hướng dẫn sử dụng Thực hành Code trực tuyến cho Sinh viên

## Tổng quan
Thực hành Code trực tuyến là một giao diện lập trình trực tuyến giống như W3Schools.com, đư<PERSON><PERSON> tích hợp vào hệ thống CS466 dành riêng cho sinh viên. Editor hỗ trợ cả Python và Perl với tính năng Trợ lý AI thông minh.

## Cách truy cập

1. **Đăng nhập vào hệ thống**: Truy cập `http://localhost:8001`
2. **Đăng nhập với tài khoản sinh viên**: 
   - Username: `student1` 
   - Password: `password123`
3. **Vào Thực hành Code trực tuyến**: <PERSON><PERSON> vào "Thự<PERSON> hành Code trực tuyến" trong sidebar

## Tính năng chính

### 1. Code Editor
- **Hỗ trợ 2 ngôn ngữ**: Python và Perl
- **Syntax highlighting**: Màu sắc code tự động
- **Line numbers**: Hiển thị số dòng
- **Auto-save**: Tự động lưu code

### 2. Examples (Ví dụ mẫu)
- **Python Examples**:
  - Hello World
  - Variables & Input
  - For Loop
  - Function
  - List Operations

- **Perl Examples**:
  - Hello World
  - Variables
  - Arrays
  - Subroutine

### 3. Trợ lý AI 🤖
- **Hỏi AI**: Nhập câu hỏi về code
- **Gợi ý thông minh**: AI phân tích code và đưa ra gợi ý
- **Áp dụng code**: Click vào gợi ý để áp dụng code

### 4. Thao tác nhanh
- **Định dạng Code**: Tự động format code đẹp
- **Chia sẻ Code**: Chia sẻ code qua clipboard
- **Tải xuống**: Tải code về máy (.py hoặc .pl)

## Cách sử dụng từng tính năng

### Viết và chạy code
1. **Chọn ngôn ngữ**: Python hoặc Perl từ dropdown
2. **Viết code**: Trong khung Trình soạn thảo Code
3. **Chạy code**: Click nút "Chạy Code" màu xanh
4. **Xem kết quả**: Trong khung "Kết quả chạy" bên phải

### Sử dụng Examples
1. **Chọn example**: Click vào example trong sidebar trái
2. **Code tự động load**: Vào editor
3. **Chạy thử**: Click "Run" để xem kết quả
4. **Chỉnh sửa**: Tùy chỉnh code theo ý muốn

### Sử dụng Trợ lý AI
1. **Nhập câu hỏi**: Trong ô "Hỏi AI về code..."
2. **Ví dụ câu hỏi**:
   - "Làm thế nào để tạo function trong Python?"
   - "Cho tôi xem ví dụ vòng lặp for"
   - "Cách đọc file trong Perl?"
   - "Sửa lỗi code của tôi"
3. **Nhận gợi ý**: AI sẽ đưa ra 2-3 gợi ý
4. **Áp dụng code**: Click vào gợi ý để thêm code

### Thao tác nhanh
- **Định dạng Code**: Làm đẹp code, chuẩn hóa indentation
- **Chia sẻ Code**: Copy code vào clipboard để chia sẻ
- **Tải xuống**: Tải file .py (Python) hoặc .pl (Perl)

## Ví dụ thực hành

### Python - Hello World với Input
```python
# Nhập tên và tuổi
name = input("Enter your name: ")
age = int(input("Enter your age: "))

# In kết quả
print(f"Hello {name}, you are {age} years old!")

# Tính năm sinh
birth_year = 2024 - age
print(f"You were born in {birth_year}")
```

### Python - Function và Loop
```python
def calculate_factorial(n):
    """Tính giai thừa của n"""
    if n <= 1:
        return 1
    else:
        return n * calculate_factorial(n - 1)

# Test function
for i in range(1, 6):
    result = calculate_factorial(i)
    print(f"{i}! = {result}")
```

### Perl - Basic Operations
```perl
#!/usr/bin/perl
use strict;
use warnings;

# Khai báo biến
my $name = "Perl";
my @numbers = (1, 2, 3, 4, 5);

# In thông tin
print "Language: $name\n";
print "Numbers: ";

# Loop qua array
foreach my $num (@numbers) {
    print "$num ";
}
print "\n";

# Subroutine
sub double_number {
    my $input = shift;
    return $input * 2;
}

# Test subroutine
my $result = double_number(10);
print "Double of 10 is: $result\n";
```

## Tips sử dụng hiệu quả

### 1. Học từ Examples
- Bắt đầu với examples đơn giản
- Chạy thử để hiểu cách hoạt động
- Chỉnh sửa và thử nghiệm

### 2. Tận dụng AI Assistant
- Hỏi câu hỏi cụ thể
- Mô tả vấn đề gặp phải
- Yêu cầu giải thích code

### 3. Thực hành thường xuyên
- Viết code mỗi ngày
- Thử các bài tập khác nhau
- Lưu code hay để tham khảo

### 4. Debug hiệu quả
- Đọc error message cẩn thận
- Sử dụng print() để debug
- Hỏi AI khi gặp lỗi

## Câu hỏi thường gặp

**Q: Tại sao code không chạy?**
A: Kiểm tra syntax, đảm bảo đã chọn đúng ngôn ngữ, và xem error message trong Output.

**Q: AI không trả lời được?**
A: Thử hỏi câu hỏi cụ thể hơn, hoặc mô tả rõ vấn đề bạn gặp phải.

**Q: Làm sao lưu code?**
A: Sử dụng nút "Save" hoặc "Download" để tải về máy.

**Q: Code bị mất khi refresh?**
A: Hệ thống có auto-save, nhưng nên thường xuyên save thủ công.

**Q: Có thể chạy code có input() không?**
A: Có, nhưng cần nhập input trước khi chạy code.

## Liên hệ hỗ trợ
- **Email**: <EMAIL>
- **Discord**: CS466 Support Channel
- **Office Hours**: Thứ 2-6, 9:00-17:00

---
**Chúc các bạn học tập hiệu quả với Thực hành Code trực tuyến! 🚀**
