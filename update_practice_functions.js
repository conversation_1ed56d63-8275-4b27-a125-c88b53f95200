// Script để cập nhật các function còn lại cho Online Coding Practice

// Thay thế tất cả các function từ W3 sang Practice
const replacements = [
    // displayW3Output -> displayPracticeOutput
    {
        from: `function displayW3Output(result, executionTime) {
            const outputDiv = document.getElementById('w3-output-content');
            let html = '';

            if (result.output) {
                html += \`<div class="w3-output-success"><strong>Output:</strong>\\n\${result.output}</div>\`;
            }

            if (result.error) {
                html += \`<div class="w3-output-error"><strong>Error:</strong>\\n\${result.error}</div>\`;
            }

            if (!result.output && !result.error) {
                html = '<div class="w3-output-info">Code executed successfully (no output)</div>';
            }

            outputDiv.innerHTML = html;
        }`,
        to: `function displayPracticeOutput(result, executionTime) {
            const outputDiv = document.getElementById('practice-output-content');
            let html = '';

            if (result.output) {
                html += \`<div class="practice-output-success"><strong>Kết quả:</strong>\\n\${result.output}</div>\`;
            }

            if (result.error) {
                html += \`<div class="practice-output-error"><strong>Lỗi:</strong>\\n\${result.error}</div>\`;
            }

            if (!result.output && !result.error) {
                html = '<div class="practice-output-info">Code đã chạy thành công (không có output)</div>';
            }

            outputDiv.innerHTML = html;
        }`
    },
    
    // getW3AISuggestion -> getPracticeAISuggestion
    {
        from: `async function getW3AISuggestion() {
            const prompt = document.getElementById('w3-ai-prompt').value.trim();
            const code = document.getElementById('w3-code-editor').value;
            const suggestionsDiv = document.getElementById('w3-ai-suggestions');
            const loadingDiv = document.getElementById('w3-ai-loading');

            if (!prompt) {
                alert('Please enter a question or request for AI assistance');
                return;
            }

            // Show loading
            loadingDiv.style.display = 'block';
            suggestionsDiv.innerHTML = '';

            try {
                const response = await fetch('/api/ai/code-suggestion', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': \`Bearer \${token}\`
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        code: code,
                        language: currentW3Language
                    })
                });

                const result = await response.json();
                loadingDiv.style.display = 'none';

                if (result.success) {
                    displayW3AISuggestions(result.suggestions);
                } else {
                    suggestionsDiv.innerHTML = \`
                        <div class="w3-ai-suggestion" style="border-color: #dc3545; background: #f8d7da;">
                            <strong>AI Error:</strong> \${result.error || 'Unable to get AI suggestion'}
                        </div>
                    \`;
                }
            } catch (error) {
                loadingDiv.style.display = 'none';
                suggestionsDiv.innerHTML = \`
                    <div class="w3-ai-suggestion" style="border-color: #dc3545; background: #f8d7da;">
                        <strong>Connection Error:</strong> \${error.message}
                    </div>
                \`;
            }

            // Clear prompt
            document.getElementById('w3-ai-prompt').value = '';
        }`,
        to: `async function getPracticeAISuggestion() {
            const prompt = document.getElementById('practice-ai-prompt').value.trim();
            const code = document.getElementById('practice-code-editor').value;
            const suggestionsDiv = document.getElementById('practice-ai-suggestions');
            const loadingDiv = document.getElementById('practice-ai-loading');

            if (!prompt) {
                alert('Vui lòng nhập câu hỏi hoặc yêu cầu hỗ trợ từ AI');
                return;
            }

            // Show loading
            loadingDiv.style.display = 'block';
            suggestionsDiv.innerHTML = '';

            try {
                const response = await fetch('/api/ai/code-suggestion', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': \`Bearer \${token}\`
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        code: code,
                        language: currentPracticeLanguage
                    })
                });

                const result = await response.json();
                loadingDiv.style.display = 'none';

                if (result.success) {
                    displayPracticeAISuggestions(result.suggestions);
                } else {
                    suggestionsDiv.innerHTML = \`
                        <div class="practice-ai-suggestion" style="border-color: #dc3545; background: #f8d7da;">
                            <strong>Lỗi AI:</strong> \${result.error || 'Không thể nhận gợi ý từ AI'}
                        </div>
                    \`;
                }
            } catch (error) {
                loadingDiv.style.display = 'none';
                suggestionsDiv.innerHTML = \`
                    <div class="practice-ai-suggestion" style="border-color: #dc3545; background: #f8d7da;">
                        <strong>Lỗi kết nối:</strong> \${error.message}
                    </div>
                \`;
            }

            // Clear prompt
            document.getElementById('practice-ai-prompt').value = '';
        }`
    }
];

// Các function khác cần thay thế:
const otherFunctions = `
function displayPracticeAISuggestions(suggestions) {
    const suggestionsDiv = document.getElementById('practice-ai-suggestions');
    
    if (!suggestions || suggestions.length === 0) {
        suggestionsDiv.innerHTML = \`
            <div class="practice-ai-suggestion">
                <strong>Không có gợi ý</strong>
                <p>Hãy thử hỏi câu hỏi cụ thể hơn về code của bạn.</p>
            </div>
        \`;
        return;
    }

    const html = suggestions.map(suggestion => \`
        <div class="practice-ai-suggestion" onclick="applyPracticeSuggestion(\\\`\${suggestion.code?.replace(/\`/g, '\\\\\\`') || ''}\\\`)">
            <strong>\${suggestion.title || 'Gợi ý AI'}</strong>
            <p style="font-size: 11px; margin: 4px 0;">\${suggestion.description || suggestion.text || ''}</p>
            \${suggestion.code ? \`<small class="text-muted">Click để áp dụng code</small>\` : ''}
        </div>
    \`).join('');

    suggestionsDiv.innerHTML = html;
}

function applyPracticeSuggestion(code) {
    if (code) {
        const editor = document.getElementById('practice-code-editor');
        const currentCode = editor.value;
        
        if (confirm('Thay thế code hiện tại bằng gợi ý AI?')) {
            editor.value = code;
        } else {
            // Append to current code
            editor.value = currentCode + '\\n\\n# Gợi ý AI:\\n' + code;
        }
        updatePracticeStatus();
    }
}

function clearPracticeCode() {
    if (confirm('Bạn có chắc muốn xóa tất cả code?')) {
        document.getElementById('practice-code-editor').value = '';
        document.getElementById('practice-output-content').innerHTML = \`
            <div class="text-center text-muted">
                <i class="fas fa-code fa-2x mb-2"></i>
                <p>Code đã được xóa. Bắt đầu viết code mới!</p>
            </div>
        \`;
        updatePracticeStatus();
    }
}

function savePracticeCode() {
    const code = document.getElementById('practice-code-editor').value;
    const language = currentPracticeLanguage;
    const filename = \`code.\${language === 'python' ? 'py' : 'pl'}\`;
    
    const blob = new Blob([code], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
    
    document.getElementById('practice-status-text').textContent = \`Đã lưu code thành \${filename}\`;
}

function formatPracticeCode() {
    // Simple code formatting
    const editor = document.getElementById('practice-code-editor');
    let code = editor.value;
    
    if (currentPracticeLanguage === 'python') {
        // Basic Python formatting
        code = code.replace(/\\t/g, '    '); // Replace tabs with 4 spaces
        code = code.replace(/\\s+$/gm, ''); // Remove trailing spaces
    }
    
    editor.value = code;
    document.getElementById('practice-status-text').textContent = 'Code đã được định dạng';
}

function sharePracticeCode() {
    const code = document.getElementById('practice-code-editor').value;
    if (navigator.share) {
        navigator.share({
            title: 'CS466 Code',
            text: code
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(code).then(() => {
            alert('Code đã được copy vào clipboard!');
        });
    }
}

function downloadPracticeCode() {
    savePracticeCode(); // Same as save function
}

function updatePracticeStatus() {
    const code = document.getElementById('practice-code-editor').value;
    const lines = code.split('\\n').length;
    const chars = code.length;
    document.getElementById('practice-status-text').textContent = \`\${lines} dòng, \${chars} ký tự\`;
}

function updatePracticeCursorPosition() {
    const editor = document.getElementById('practice-code-editor');
    const start = editor.selectionStart;
    const text = editor.value.substring(0, start);
    const lines = text.split('\\n');
    const line = lines.length;
    const column = lines[lines.length - 1].length + 1;
    
    document.getElementById('practice-cursor-position').textContent = \`Dòng \${line}, Cột \${column}\`;
}
`;

console.log("Các function cần thay thế đã được chuẩn bị. Sử dụng str-replace-editor để cập nhật từng function.");
