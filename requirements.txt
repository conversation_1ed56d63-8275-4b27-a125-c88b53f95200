# CS466 Learning System - Dependencies
# Core FastAPI framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Database
sqlite3  # Built-in Python

# Template Engine
jinja2==3.1.2

# HTTP Requests for AI integration
requests==2.31.0
aiohttp==3.9.1

# File handling
python-multipart==0.0.6

# Date/Time utilities
python-dateutil==2.8.2

# Development utilities (optional)
pytest==7.4.3
pytest-asyncio==0.21.1

# System utilities
psutil==5.9.6

# JSON handling (built-in)
# json - Built-in Python
# uuid - Built-in Python  
# datetime - Built-in Python
# subprocess - Built-in Python
# threading - Built-in Python
# asyncio - Built-in Python
# sqlite3 - Built-in Python

# Note: Some modules are built-in to Python 3.8+
# No additional installation required for:
# - sqlite3, json, uuid, datetime, subprocess, threading, asyncio, sys, os, re 