<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đăng nhập - CS466 Learning System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-top: 10vh;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .login-header p {
            color: #666;
            margin-bottom: 0;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-control {
            border-radius: 8px;
            border: 1px solid #ddd;
            padding: 0.75rem;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            border-color: #667eea;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .demo-accounts {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1.5rem;
        }
        
        .demo-accounts h6 {
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .demo-account {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .demo-account:last-child {
            border-bottom: none;
        }
        
        .demo-account .btn {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        
        .loading {
            display: none;
        }
        
        .alert {
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .input-group {
            position: relative;
        }
        
        .input-group-text {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px 0 0 8px;
        }
        
        .form-control.with-icon {
            border-radius: 0 8px 8px 0;
        }
        
        @media (max-width: 576px) {
            .login-container {
                margin: 2rem;
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="login-header">
                <h1><i class="fas fa-graduation-cap text-primary"></i> CS466</h1>
                <p>Hệ thống học tập Perl & Python</p>
            </div>
            
            <div id="alert-container"></div>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="username" class="form-label">Tên đăng nhập</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                        <input type="text" class="form-control with-icon" id="username" name="username" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">Mật khẩu</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                        <input type="password" class="form-control with-icon" id="password" name="password" required>
                        <button type="button" class="btn btn-outline-secondary" id="togglePassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="rememberMe" name="rememberMe">
                        <label class="form-check-label" for="rememberMe">
                            Ghi nhớ đăng nhập
                        </label>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-login btn-primary w-100">
                    <span class="login-text">Đăng nhập</span>
                    <span class="loading">
                        <i class="fas fa-spinner fa-spin"></i> Đang đăng nhập...
                    </span>
                </button>
            </form>
            
            <div class="demo-accounts">
                <h6><i class="fas fa-info-circle"></i> Tài khoản demo:</h6>
                <div class="demo-account">
                    <div>
                        <strong>Giáo viên:</strong> teacher / teacher123
                    </div>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="fillDemoAccount('teacher', 'teacher123')">
                        Điền
                    </button>
                </div>
                <div class="demo-account">
                    <div>
                        <strong>Sinh viên:</strong> student / student123
                    </div>
                    <button type="button" class="btn btn-outline-success btn-sm" onclick="fillDemoAccount('student', 'student123')">
                        Điền
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let isLoading = false;
        
        // DOM elements
        const loginForm = document.getElementById('loginForm');
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');
        const togglePasswordBtn = document.getElementById('togglePassword');
        const alertContainer = document.getElementById('alert-container');
        const loginText = document.querySelector('.login-text');
        const loadingSpinner = document.querySelector('.loading');
        
        // Event listeners
        loginForm.addEventListener('submit', handleLogin);
        togglePasswordBtn.addEventListener('click', togglePassword);
        usernameInput.addEventListener('input', clearErrors);
        passwordInput.addEventListener('input', clearErrors);
        
        // Load saved credentials
        window.addEventListener('load', loadSavedCredentials);
        
        // Handle form submission
        async function handleLogin(e) {
            e.preventDefault();
            
            if (isLoading) return;
            
            const username = usernameInput.value.trim();
            const password = passwordInput.value.trim();
            const rememberMe = document.getElementById('rememberMe').checked;
            
            // Validation
            if (!username || !password) {
                showAlert('Vui lòng điền đầy đủ thông tin đăng nhập!', 'danger');
                return;
            }
            
            if (username.length < 3) {
                showAlert('Tên đăng nhập phải có ít nhất 3 ký tự!', 'danger');
                return;
            }
            
            if (password.length < 6) {
                showAlert('Mật khẩu phải có ít nhất 6 ký tự!', 'danger');
                return;
            }
            
            // Start loading
            setLoading(true);
            
            try {
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);
                
                const response = await fetch('/login', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Save credentials if remember me is checked
                    if (rememberMe) {
                        localStorage.setItem('savedUsername', username);
                        localStorage.setItem('rememberMe', 'true');
                    } else {
                        localStorage.removeItem('savedUsername');
                        localStorage.removeItem('rememberMe');
                    }
                    
                    // Save token
                    localStorage.setItem('access_token', data.access_token);
                    localStorage.setItem('user_role', data.role);
                    
                    showAlert('Đăng nhập thành công! Đang chuyển hướng...', 'success');
                    
                    // Redirect based on role
                    setTimeout(() => {
                        window.location.href = `/dashboard/${data.role}`;
                    }, 1500);
                    
                } else {
                    showAlert(data.detail || 'Đăng nhập thất bại!', 'danger');
                }
                
            } catch (error) {
                console.error('Login error:', error);
                showAlert('Lỗi kết nối! Vui lòng thử lại.', 'danger');
            } finally {
                setLoading(false);
            }
        }
        
        // Toggle password visibility
        function togglePassword() {
            const type = passwordInput.type === 'password' ? 'text' : 'password';
            passwordInput.type = type;
            
            const icon = togglePasswordBtn.querySelector('i');
            icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
        }
        
        // Fill demo account
        function fillDemoAccount(username, password) {
            usernameInput.value = username;
            passwordInput.value = password;
            clearErrors();
            
            // Add visual feedback
            usernameInput.classList.add('is-valid');
            passwordInput.classList.add('is-valid');
            
            setTimeout(() => {
                usernameInput.classList.remove('is-valid');
                passwordInput.classList.remove('is-valid');
            }, 2000);
        }
        
        // Show alert message
        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            alertContainer.innerHTML = alertHtml;
            
            // Auto dismiss after 5 seconds
            setTimeout(() => {
                const alert = alertContainer.querySelector('.alert');
                if (alert) {
                    alert.classList.remove('show');
                    setTimeout(() => {
                        alertContainer.innerHTML = '';
                    }, 300);
                }
            }, 5000);
        }
        
        // Clear validation errors
        function clearErrors() {
            usernameInput.classList.remove('is-invalid');
            passwordInput.classList.remove('is-invalid');
        }
        
        // Set loading state
        function setLoading(loading) {
            isLoading = loading;
            const submitBtn = loginForm.querySelector('button[type="submit"]');
            
            if (loading) {
                submitBtn.disabled = true;
                loginText.style.display = 'none';
                loadingSpinner.style.display = 'inline';
            } else {
                submitBtn.disabled = false;
                loginText.style.display = 'inline';
                loadingSpinner.style.display = 'none';
            }
        }
        
        // Load saved credentials
        function loadSavedCredentials() {
            const savedUsername = localStorage.getItem('savedUsername');
            const rememberMe = localStorage.getItem('rememberMe');
            
            if (savedUsername && rememberMe === 'true') {
                usernameInput.value = savedUsername;
                document.getElementById('rememberMe').checked = true;
            }
            
            // Check if user is already logged in
            const token = localStorage.getItem('access_token');
            const role = localStorage.getItem('user_role');
            
            if (token && role) {
                // Validate token by making a request
                fetch(`/dashboard/${role}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => {
                    if (response.ok) {
                        window.location.href = `/dashboard/${role}`;
                    } else {
                        // Token invalid, clear it
                        localStorage.removeItem('access_token');
                        localStorage.removeItem('user_role');
                    }
                })
                .catch(error => {
                    console.error('Token validation error:', error);
                });
            }
        }
        
        // Handle Enter key in password field
        passwordInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleLogin(e);
            }
        });
        
        // Add input animations
        [usernameInput, passwordInput].forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('focused');
                if (this.value.trim() !== '') {
                    this.classList.add('has-value');
                } else {
                    this.classList.remove('has-value');
                }
            });
        });
    </script>
</body>
</html> 