/* Global Styles */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: var(--light-color);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Utility Classes */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

.spin {
    animation: spin 1s linear infinite;
}

/* Custom Buttons */
.btn-gradient {
    background: var(--primary-gradient);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: var(--transition);
    cursor: pointer;
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: white;
    text-decoration: none;
}

.btn-gradient:active {
    transform: translateY(0);
}

.btn-outline-gradient {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: var(--transition);
    cursor: pointer;
}

.btn-outline-gradient:hover {
    background: var(--primary-gradient);
    color: white;
    text-decoration: none;
}

/* Cards */
.card-gradient {
    background: white;
    border: none;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card-gradient:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header-gradient {
    background: var(--primary-gradient);
    color: white;
    padding: 1rem 1.5rem;
    border-bottom: none;
    font-weight: 600;
}

/* Forms */
.form-control-custom {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 0.75rem;
    transition: var(--transition);
    font-size: 1rem;
}

.form-control-custom:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

.form-label-custom {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    display: block;
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Alerts */
.alert-custom {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid;
    animation: fadeIn 0.3s ease-out;
}

.alert-success-custom {
    background-color: #d4edda;
    border-left-color: var(--success-color);
    color: #155724;
}

.alert-warning-custom {
    background-color: #fff3cd;
    border-left-color: var(--warning-color);
    color: #856404;
}

.alert-danger-custom {
    background-color: #f8d7da;
    border-left-color: var(--danger-color);
    color: #721c24;
}

.alert-info-custom {
    background-color: #d1ecf1;
    border-left-color: var(--info-color);
    color: #0c5460;
}

/* Badges */
.badge-gradient {
    background: var(--primary-gradient);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.badge-outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Progress Bars */
.progress-custom {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar-custom {
    height: 100%;
    background: var(--primary-gradient);
    transition: width 0.6s ease;
}

/* Code Editor Styles */
.code-editor-container {
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    overflow: hidden;
    background: white;
}

.code-editor-header {
    background: #f8f9fa;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.code-editor-body {
    min-height: 400px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Search Components */
.search-container {
    position: relative;
}

.search-input {
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 1rem;
    transition: var(--transition);
    width: 100%;
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e9ecef;
    border-top: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: var(--box-shadow);
}

.search-suggestion {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 1px solid #f8f9fa;
}

.search-suggestion:hover {
    background-color: var(--light-color);
}

.search-suggestion:last-child {
    border-bottom: none;
}

/* Filter Tags */
.filter-container {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin: 1rem 0;
}

.filter-tag {
    background: #e9ecef;
    color: #495057;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
}

.filter-tag:hover {
    background-color: #dee2e6;
}

.filter-tag.active {
    background: var(--primary-gradient);
    color: white;
}

/* Modal Enhancements */
.modal-custom .modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-custom .modal-header {
    background: var(--primary-gradient);
    color: white;
    border-bottom: none;
    border-radius: 10px 10px 0 0;
}

.modal-custom .modal-header .btn-close {
    filter: invert(1);
}

/* Timeline */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--primary-gradient);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-left: 1rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -1.75rem;
    top: 1.5rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--primary-color);
    border: 3px solid white;
    box-shadow: 0 0 0 3px var(--primary-color);
}

/* Statistics Cards */
.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    opacity: 0.1;
    position: absolute;
    top: 1rem;
    right: 1rem;
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .btn-gradient,
    .btn-outline-gradient {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .stat-card {
        padding: 1.5rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .timeline {
        padding-left: 1rem;
    }
    
    .timeline-item {
        margin-left: 0.5rem;
    }
    
    .timeline-item::before {
        left: -1.25rem;
    }
}

@media (max-width: 576px) {
    .filter-container {
        justify-content: center;
    }
    
    .search-input {
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .modal-custom .modal-dialog {
        margin: 1rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #1a1a1a;
        --dark-color: #ffffff;
    }
    
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card-gradient {
        background: #2d2d2d;
        color: #ffffff;
    }
    
    .form-control-custom {
        background-color: #2d2d2d;
        border-color: #444;
        color: #ffffff;
    }
    
    .form-control-custom:focus {
        background-color: #2d2d2d;
        border-color: var(--primary-color);
        color: #ffffff;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .sidebar,
    .btn,
    .modal {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card-gradient {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
} 