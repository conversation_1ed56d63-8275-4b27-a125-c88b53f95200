<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Giáo Viên - CS466</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }

        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            color: white !important;
            font-weight: bold;
        }

        .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.8) !important;
            transition: color 0.3s;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
        }

        .sidebar {
            background: white;
            min-height: calc(100vh - 56px);
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        }

        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            margin: 0.25rem;
            transition: all 0.3s;
        }

        .sidebar .nav-link:hover {
            background-color: #e9ecef;
            color: #495057;
        }

        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .main-content {
            padding: 2rem;
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }

        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }

        .stats-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .assignment-form {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .ai-generator {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            padding: 2rem;
            color: white;
            margin-bottom: 2rem;
        }

        .ai-generator h3 {
            color: white;
            margin-bottom: 1rem;
        }

        .question-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            color: #495057;
        }

        .question-card h6 {
            color: #667eea;
            margin-bottom: 1rem;
        }

        .option-list {
            margin-left: 1rem;
        }

        .option-list li {
            margin-bottom: 0.5rem;
        }

        .option-list li.correct {
            color: #28a745;
            font-weight: bold;
        }

        .loading-spinner {
            text-align: center;
            padding: 2rem;
        }

        .no-data {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .no-data i {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .assignment-list {
            margin-top: 2rem;
        }

        .assignment-item {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .assignment-item h5 {
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .assignment-actions {
            margin-top: 1rem;
        }

        .submission-count {
            background: #e9ecef;
            border-radius: 20px;
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.5rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 56px;
                left: -250px;
                width: 250px;
                height: calc(100vh - 56px);
                z-index: 1000;
                transition: left 0.3s;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                padding: 1rem;
            }
        }
    </style>
</head>

<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chalkboard-teacher"></i> CS466 - Giáo Viên
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button"
                            data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <span id="username-display">Giáo viên</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showProfile()"><i class="fas fa-user"></i> Hồ
                                    sơ</a></li>
                            <li><a class="dropdown-item" href="#" onclick="showSettings()"><i class="fas fa-cog"></i>
                                    Cài đặt</a></li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i>
                                    Đăng xuất</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar">
                <nav class="nav flex-column pt-3">
                    <a class="nav-link active" href="#" onclick="showDashboard()">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a class="nav-link" href="#" onclick="showAssignments()">
                        <i class="fas fa-tasks"></i> Quản lý bài tập
                    </a>
                    <a class="nav-link" href="#" onclick="showCreateAssignment()">
                        <i class="fas fa-plus-circle"></i> Tạo bài tập
                    </a>
                    <a class="nav-link" href="#" onclick="showAIGenerator()">
                        <i class="fas fa-robot"></i> AI Generator
                    </a>
                    <a class="nav-link" href="#" onclick="showReports()">
                        <i class="fas fa-chart-bar"></i> Báo cáo và thống kê
                    </a>
                    <a class="nav-link" href="#" onclick="showStudentProgress()">
                        <i class="fas fa-users"></i> Tiến độ sinh viên
                    </a>
                    <a class="nav-link" href="#" onclick="showDetailedReports()">
                        <i class="fas fa-file-alt"></i> Báo cáo chi tiết
                    </a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 main-content">
                <div id="content-area">
                    <!-- Dashboard Content -->
                    <div id="dashboard-content">
                        <h2>Dashboard Giáo Viên <i class="fas fa-chalkboard-teacher"></i></h2>
                        <p class="text-muted">Tổng quan về hoạt động giảng dạy</p>

                        <!-- Stats Cards -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <div class="stats-icon">
                                        <i class="fas fa-tasks"></i>
                                    </div>
                                    <div class="stats-number" id="total-assignments">0</div>
                                    <div class="text-muted">Tổng bài tập</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <div class="stats-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="stats-number" id="total-students">0</div>
                                    <div class="text-muted">Sinh viên</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <div class="stats-icon">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <div class="stats-number" id="total-submissions">0</div>
                                    <div class="text-muted">Bài nộp</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <div class="stats-icon">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="stats-number" id="avg-score">0%</div>
                                    <div class="text-muted">Điểm trung bình</div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-bolt"></i> Thao tác nhanh</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <button class="btn btn-primary w-100" onclick="showCreateAssignment()">
                                                    <i class="fas fa-plus"></i> Tạo bài tập mới
                                                </button>
                                            </div>
                                            <div class="col-md-3">
                                                <a href="/ai/question-generator" class="btn btn-success w-100">
                                                    <i class="fas fa-robot"></i> Sinh câu hỏi AI
                                                </a>
                                            </div>
                                            <div class="col-md-3">
                                                <a href="/search" class="btn btn-primary w-100">
                                                    <i class="fas fa-search"></i> Tìm kiếm nâng cao
                                                </a>
                                            </div>
                                            <div class="col-md-3">
                                                <button class="btn btn-info w-100" onclick="showReports()">
                                                    <i class="fas fa-chart-bar"></i> Xem báo cáo
                                                </button>
                                            </div>
                                            <div class="col-md-3">
                                                <button class="btn btn-warning w-100" onclick="showAssignments()">
                                                    <i class="fas fa-list"></i> Quản lý bài tập
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activities -->
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-clock"></i> Hoạt động gần đây</h5>
                            </div>
                            <div class="card-body">
                                <div id="recent-activities">
                                    <div class="loading-spinner">
                                        <i class="fas fa-spinner fa-spin"></i> Đang tải...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Assignments Management -->
                    <div id="assignments-content" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h2><i class="fas fa-tasks"></i> Quản lý bài tập</h2>
                            <button class="btn btn-primary" onclick="showCreateAssignment()">
                                <i class="fas fa-plus"></i> Tạo bài tập mới
                            </button>
                        </div>

                        <div id="assignments-list">
                            <div class="loading-spinner">
                                <i class="fas fa-spinner fa-spin"></i> Đang tải bài tập...
                            </div>
                        </div>
                    </div>

                    <!-- Create Assignment -->
                    <div id="create-assignment-content" style="display: none;">
                        <h2><i class="fas fa-plus-circle"></i> Tạo bài tập mới</h2>

                        <div class="assignment-form">
                            <form id="assignment-form">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="assignment-title" class="form-label">Tiêu đề bài tập</label>
                                            <input type="text" class="form-control" id="assignment-title" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="assignment-language" class="form-label">Ngôn ngữ</label>
                                            <select class="form-select" id="assignment-language" required>
                                                <option value="">Chọn ngôn ngữ</option>
                                                <option value="python">Python</option>
                                                <option value="perl">Perl</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="assignment-type" class="form-label">Loại bài tập</label>
                                            <select class="form-select" id="assignment-type" required>
                                                <option value="">Chọn loại bài tập</option>
                                                <option value="code">Lập trình</option>
                                                <option value="quiz">Trắc nghiệm</option>
                                                <option value="file">Upload file</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="assignment-deadline" class="form-label">Deadline</label>
                                            <input type="datetime-local" class="form-control" id="assignment-deadline"
                                                required>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="assignment-description" class="form-label">Mô tả bài tập</label>
                                    <textarea class="form-control" id="assignment-description" rows="4"
                                        required></textarea>
                                </div>

                                <div class="text-end">
                                    <button type="button" class="btn btn-secondary me-2"
                                        onclick="resetAssignmentForm()">
                                        <i class="fas fa-undo"></i> Làm lại
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Tạo bài tập
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- AI Generator -->
                    <div id="ai-generator-content" style="display: none;">
                        <h2><i class="fas fa-robot"></i> Sinh câu hỏi tự động bằng AI</h2>

                        <div class="ai-generator">
                            <h3><i class="fas fa-magic"></i> AI Question Generator</h3>
                            <p>Sử dụng AI để tạo ra các câu hỏi trắc nghiệm chất lượng cao</p>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="ai-topic" class="form-label">Chủ đề</label>
                                        <input type="text" class="form-control" id="ai-topic"
                                            placeholder="Ví dụ: Python cơ bản">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="ai-difficulty" class="form-label">Độ khó</label>
                                        <select class="form-select" id="ai-difficulty">
                                            <option value="easy">Dễ</option>
                                            <option value="medium">Trung bình</option>
                                            <option value="hard">Khó</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="ai-count" class="form-label">Số lượng câu hỏi</label>
                                        <input type="number" class="form-control" id="ai-count" value="5" min="1"
                                            max="20">
                                    </div>
                                </div>
                            </div>

                            <div class="text-center">
                                <button class="btn btn-light btn-lg" onclick="generateQuestions()">
                                    <i class="fas fa-magic"></i> Sinh câu hỏi
                                </button>
                            </div>
                        </div>

                        <div id="generated-questions">
                            <div class="no-data">
                                <i class="fas fa-robot"></i>
                                <h4>Chưa có câu hỏi nào</h4>
                                <p>Sử dụng AI Generator để tạo câu hỏi</p>
                            </div>
                        </div>
                    </div>

                    <!-- Reports -->
                    <div id="reports-content" style="display: none;">
                        <h2><i class="fas fa-chart-bar"></i> Báo cáo và thống kê</h2>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-chart-pie"></i> Thống kê bài tập</h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="assignments-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-chart-line"></i> Tiến độ sinh viên</h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="progress-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Export Actions -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-download"></i> Xuất báo cáo</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <button class="btn btn-success w-100" onclick="exportExcelReport()">
                                                    <i class="fas fa-file-excel"></i> Xuất Excel
                                                </button>
                                            </div>
                                            <div class="col-md-4">
                                                <button class="btn btn-danger w-100" onclick="exportPDFReport()">
                                                    <i class="fas fa-file-pdf"></i> Xuất PDF
                                                </button>
                                            </div>
                                            <div class="col-md-4">
                                                <button class="btn btn-info w-100" onclick="showDetailedReports()">
                                                    <i class="fas fa-eye"></i> Xem chi tiết
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header">
                                <h5><i class="fas fa-table"></i> Chi tiết bài nộp</h5>
                            </div>
                            <div class="card-body">
                                <div id="submissions-table">
                                    <div class="loading-spinner">
                                        <i class="fas fa-spinner fa-spin"></i> Đang tải dữ liệu...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Student Progress -->
                    <div id="student-progress-content" style="display: none;">
                        <h2><i class="fas fa-users"></i> Tiến độ sinh viên</h2>

                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5><i class="fas fa-table"></i> Bảng tiến độ sinh viên</h5>
                                    <button class="btn btn-primary" onclick="refreshStudentProgress()">
                                        <i class="fas fa-sync-alt"></i> Làm mới
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="student-progress-table">
                                        <thead>
                                            <tr>
                                                <th>Sinh viên</th>
                                                <th>Tổng bài tập</th>
                                                <th>Đã hoàn thành</th>
                                                <th>Tỷ lệ hoàn thành</th>
                                                <th>Điểm trung bình</th>
                                                <th>Lần nộp cuối</th>
                                            </tr>
                                        </thead>
                                        <tbody id="student-progress-tbody">
                                            <tr>
                                                <td colspan="6" class="text-center">
                                                    <i class="fas fa-spinner fa-spin"></i> Đang tải dữ liệu...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Reports -->
                    <div id="detailed-reports-content" style="display: none;">
                        <h2><i class="fas fa-file-alt"></i> Báo cáo chi tiết</h2>

                        <!-- Assignment Statistics -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-tasks"></i> Thống kê bài tập</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="assignment-stats-table">
                                        <thead>
                                            <tr>
                                                <th>Bài tập</th>
                                                <th>Ngôn ngữ</th>
                                                <th>Độ khó</th>
                                                <th>Deadline</th>
                                                <th>Số bài nộp</th>
                                                <th>Điểm TB</th>
                                                <th>Điểm thấp nhất</th>
                                                <th>Điểm cao nhất</th>
                                            </tr>
                                        </thead>
                                        <tbody id="assignment-stats-tbody">
                                            <tr>
                                                <td colspan="8" class="text-center">
                                                    <i class="fas fa-spinner fa-spin"></i> Đang tải dữ liệu...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Detailed Submissions -->
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5><i class="fas fa-list"></i> Chi tiết bài nộp</h5>
                                    <select class="form-select" id="assignment-filter" style="width: auto;">
                                        <option value="">Tất cả bài tập</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="detailed-submissions-table">
                                        <thead>
                                            <tr>
                                                <th>Bài tập</th>
                                                <th>Sinh viên</th>
                                                <th>Điểm</th>
                                                <th>Thời gian nộp</th>
                                                <th>Ngôn ngữ</th>
                                                <th>Độ khó</th>
                                                <th>Phản hồi</th>
                                            </tr>
                                        </thead>
                                        <tbody id="detailed-submissions-tbody">
                                            <tr>
                                                <td colspan="7" class="text-center">
                                                    <i class="fas fa-spinner fa-spin"></i> Đang tải dữ liệu...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Global variables
        let currentAssignments = [];
        const token = localStorage.getItem('access_token');

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function () {
            if (!token) {
                window.location.href = '/';
                return;
            }

            loadUserInfo();
            loadDashboardData();
            loadAssignments();
        });

        // Load user info
        function loadUserInfo() {
            const role = localStorage.getItem('user_role');
            const username = role === 'teacher' ? 'Giáo viên' : 'Sinh viên';
            document.getElementById('username-display').textContent = username;
        }

        // Logout function
        function logout() {
            localStorage.removeItem('access_token');
            localStorage.removeItem('user_role');
            window.location.href = '/';
        }

        // Profile and Settings functions
        function showProfile() {
            alert('Chức năng hồ sơ đang được phát triển');
        }

        function showSettings() {
            alert('Chức năng cài đặt đang được phát triển');
        }

        // Navigation functions
        function showDashboard() {
            hideAllContent();
            document.getElementById('dashboard-content').style.display = 'block';
            updateActiveNav('dashboard');
            loadDashboardData();
        }

        function showAssignments() {
            hideAllContent();
            document.getElementById('assignments-content').style.display = 'block';
            updateActiveNav('assignments');
            loadAssignments();
        }

        function showCreateAssignment() {
            hideAllContent();
            document.getElementById('create-assignment-content').style.display = 'block';
            updateActiveNav('create-assignment');
        }

        function showAIGenerator() {
            // Debug: Check if user is authenticated
            console.log('Token:', token);
            console.log('Redirecting to AI Generator...');

            // Redirect to dedicated AI Question Generator page
            window.location.href = '/ai/question-generator';
        }

        function showReports() {
            hideAllContent();
            document.getElementById('reports-content').style.display = 'block';
            updateActiveNav('reports');
            loadReports();
            loadTeacherStatistics();
        }

        function showStudentProgress() {
            hideAllContent();
            document.getElementById('student-progress-content').style.display = 'block';
            updateActiveNav('student-progress');
            loadStudentProgress();
        }

        function showDetailedReports() {
            hideAllContent();
            document.getElementById('detailed-reports-content').style.display = 'block';
            updateActiveNav('detailed-reports');
            loadDetailedReports();
        }

        function hideAllContent() {
            const contents = ['dashboard-content', 'assignments-content', 'create-assignment-content', 'ai-generator-content', 'reports-content', 'student-progress-content', 'detailed-reports-content'];
            contents.forEach(id => {
                document.getElementById(id).style.display = 'none';
            });
        }

        function updateActiveNav(activeId) {
            document.querySelectorAll('.sidebar .nav-link').forEach(link => {
                link.classList.remove('active');
            });

            const mapping = {
                'dashboard': 0,
                'assignments': 1,
                'create-assignment': 2,
                'ai-generator': 3,
                'reports': 4
            };

            const links = document.querySelectorAll('.sidebar .nav-link');
            if (links[mapping[activeId]]) {
                links[mapping[activeId]].classList.add('active');
            }
        }

        // Load dashboard data
        async function loadDashboardData() {
            try {
                // Mock data for demonstration
                const stats = {
                    totalAssignments: 15,
                    totalStudents: 25,
                    totalSubmissions: 87,
                    avgScore: 85
                };

                document.getElementById('total-assignments').textContent = stats.totalAssignments;
                document.getElementById('total-students').textContent = stats.totalStudents;
                document.getElementById('total-submissions').textContent = stats.totalSubmissions;
                document.getElementById('avg-score').textContent = stats.avgScore + '%';

                // Load recent activities
                loadRecentActivities();
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        function loadRecentActivities() {
            const activities = [
                { time: '2 giờ trước', text: 'Sinh viên Nguyễn Văn A nộp bài "Hello World Python"', type: 'submission' },
                { time: '5 giờ trước', text: 'Tạo bài tập mới "Vòng lặp trong Python"', type: 'assignment' },
                { time: '1 ngày trước', text: 'Sinh viên Trần Thị B hoàn thành bài "Hàm trong Python"', type: 'completion' },
                { time: '2 ngày trước', text: 'Sử dụng AI tạo 10 câu hỏi về Python cơ bản', type: 'ai' }
            ];

            const html = activities.map(activity => {
                const iconClass = activity.type === 'submission' ? 'fas fa-file-alt text-info' :
                    activity.type === 'assignment' ? 'fas fa-plus-circle text-success' :
                        activity.type === 'completion' ? 'fas fa-check-circle text-success' :
                            'fas fa-robot text-warning';

                return `
                    <div class="d-flex align-items-center py-2 border-bottom">
                        <div class="me-3">
                            <i class="${iconClass}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div>${activity.text}</div>
                            <small class="text-muted">${activity.time}</small>
                        </div>
                    </div>
                `;
            }).join('');

            document.getElementById('recent-activities').innerHTML = html;
        }

        // Load assignments
        async function loadAssignments() {
            try {
                const response = await fetch('/api/teacher/assignments', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const assignments = await response.json();
                    currentAssignments = assignments;
                    displayAssignments(assignments);
                } else {
                    throw new Error('Failed to load assignments');
                }
            } catch (error) {
                console.error('Error loading assignments:', error);
                document.getElementById('assignments-list').innerHTML =
                    '<div class="no-data"><i class="fas fa-exclamation-triangle"></i><h4>Lỗi tải dữ liệu</h4></div>';
            }
        }

        function displayAssignments(assignments) {
            const container = document.getElementById('assignments-list');

            if (assignments.length === 0) {
                container.innerHTML = `
                    <div class="no-data">
                        <i class="fas fa-tasks"></i>
                        <h4>Chưa có bài tập nào</h4>
                        <p>Tạo bài tập đầu tiên của bạn</p>
                    </div>
                `;
                return;
            }

            const html = assignments.map(assignment => `
                <div class="assignment-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h5>${assignment.title}</h5>
                            <p class="text-muted">${assignment.description}</p>
                            <div>
                                <span class="badge bg-primary">${assignment.language}</span>
                                <span class="badge bg-secondary">${assignment.assignment_type}</span>
                                <span class="submission-count">
                                    <i class="fas fa-file-alt"></i> ${assignment.submission_count || 0} bài nộp
                                </span>
                            </div>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> ${formatDate(assignment.deadline)}
                            </small>
                        </div>
                    </div>
                    <div class="assignment-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="viewAssignmentDetails(${assignment.id})">
                            <i class="fas fa-eye"></i> Xem chi tiết
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="viewSubmissions(${assignment.id})">
                            <i class="fas fa-file-alt"></i> Xem bài nộp
                        </button>
                        <button class="btn btn-sm btn-outline-warning" onclick="editAssignment(${assignment.id})">
                            <i class="fas fa-edit"></i> Chỉnh sửa
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteAssignment(${assignment.id})">
                            <i class="fas fa-trash"></i> Xóa
                        </button>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        // Create assignment
        document.getElementById('assignment-form').addEventListener('submit', async function (e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('title', document.getElementById('assignment-title').value);
            formData.append('description', document.getElementById('assignment-description').value);
            formData.append('assignment_type', document.getElementById('assignment-type').value);
            formData.append('language', document.getElementById('assignment-language').value);
            formData.append('deadline', document.getElementById('assignment-deadline').value);

            try {
                const response = await fetch('/assignment/create', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    alert('Tạo bài tập thành công!');
                    resetAssignmentForm();
                    loadAssignments();
                } else {
                    throw new Error('Failed to create assignment');
                }
            } catch (error) {
                console.error('Error creating assignment:', error);
                alert('Lỗi tạo bài tập! Vui lòng thử lại.');
            }
        });

        function resetAssignmentForm() {
            document.getElementById('assignment-form').reset();
        }

        // AI Question Generator
        async function generateQuestions() {
            const topic = document.getElementById('ai-topic').value.trim();
            const difficulty = document.getElementById('ai-difficulty').value;
            const count = parseInt(document.getElementById('ai-count').value);

            if (!topic) {
                alert('Vui lòng nhập chủ đề!');
                return;
            }

            document.getElementById('generated-questions').innerHTML = `
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i> AI đang sinh câu hỏi...
                </div>
            `;

            try {
                const formData = new FormData();
                formData.append('topic', topic);
                formData.append('difficulty', difficulty);
                formData.append('count', count);

                const response = await fetch('/ai/generate-questions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    displayGeneratedQuestions(result.questions);
                } else {
                    throw new Error('Failed to generate questions');
                }
            } catch (error) {
                console.error('Error generating questions:', error);
                document.getElementById('generated-questions').innerHTML = `
                    <div class="no-data">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h4>Lỗi sinh câu hỏi</h4>
                        <p>Vui lòng thử lại sau</p>
                    </div>
                `;
            }
        }

        function displayGeneratedQuestions(questions) {
            const container = document.getElementById('generated-questions');

            if (questions.length === 0) {
                container.innerHTML = `
                    <div class="no-data">
                        <i class="fas fa-robot"></i>
                        <h4>Không tạo được câu hỏi</h4>
                        <p>Vui lòng thử với chủ đề khác</p>
                    </div>
                `;
                return;
            }

            const html = questions.map((question, index) => `
                <div class="question-card">
                    <h6>Câu ${index + 1}: ${question.question}</h6>
                    <ul class="option-list">
                        ${question.options.map((option, optionIndex) => `
                            <li class="${optionIndex === question.correct_index ? 'correct' : ''}">
                                ${String.fromCharCode(65 + optionIndex)}. ${option}
                                ${optionIndex === question.correct_index ? ' <i class="fas fa-check"></i>' : ''}
                            </li>
                        `).join('')}
                    </ul>
                    <div class="text-muted">
                        <small><strong>Giải thích:</strong> ${question.explanation}</small>
                    </div>
                </div>
            `).join('');

            container.innerHTML = `
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4>Câu hỏi đã tạo (${questions.length})</h4>
                    <button class="btn btn-success" onclick="saveGeneratedQuestions()">
                        <i class="fas fa-save"></i> Lưu làm bài tập
                    </button>
                </div>
                ${html}
            `;
        }

        function saveGeneratedQuestions() {
            alert('Chức năng lưu câu hỏi đang được phát triển');
        }

        // Reports
        function loadReports() {
            loadAssignmentsChart();
            loadProgressChart();
            loadSubmissionsTable();
        }

        // ================================
        // TEACHER STATISTICS & REPORTS
        // ================================

        async function loadTeacherStatistics() {
            try {
                const response = await fetch('/api/teacher/statistics', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const result = await response.json();

                if (result.success) {
                    const stats = result.data;

                    // Update overview cards
                    document.getElementById('report-total-assignments').textContent = stats.total_assignments;
                    document.getElementById('report-total-students').textContent = stats.total_students;
                    document.getElementById('report-total-submissions').textContent = stats.total_submissions;
                    document.getElementById('report-avg-score').textContent = stats.avg_score + '%';

                    // Load charts
                    loadGradeDistributionChart();
                    loadSubmissionTimelineChart();
                }
            } catch (error) {
                console.error('Error loading teacher statistics:', error);
            }
        }

        async function loadStudentProgress() {
            try {
                const response = await fetch('/api/teacher/student-progress', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const result = await response.json();

                if (result.success) {
                    displayStudentProgressTable(result.data);
                }
            } catch (error) {
                console.error('Error loading student progress:', error);
            }
        }

        function displayStudentProgressTable(students) {
            const tbody = document.getElementById('student-progress-tbody');

            if (students.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center">Chưa có dữ liệu</td></tr>';
                return;
            }

            const html = students.map(student => {
                const progressClass = student.progress_rate >= 80 ? 'text-success' :
                                    student.progress_rate >= 60 ? 'text-warning' : 'text-danger';

                const scoreClass = student.avg_score >= 80 ? 'text-success' :
                                 student.avg_score >= 60 ? 'text-warning' : 'text-danger';

                return `
                    <tr>
                        <td><strong>${student.full_name}</strong><br><small class="text-muted">${student.username}</small></td>
                        <td>${student.total_assignments}</td>
                        <td>${student.completed_assignments}</td>
                        <td><span class="${progressClass}">${student.progress_rate}%</span></td>
                        <td><span class="${scoreClass}">${student.avg_score}</span></td>
                        <td>${student.last_submission ? formatDateTime(student.last_submission) : 'Chưa nộp'}</td>
                    </tr>
                `;
            }).join('');

            tbody.innerHTML = html;
        }

        async function loadDetailedReports() {
            try {
                // Load assignment statistics
                const assignmentResponse = await fetch('/api/teacher/assignment-statistics', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (assignmentResponse.ok) {
                    const assignmentResult = await assignmentResponse.json();
                    if (assignmentResult.success) {
                        displayAssignmentStatistics(assignmentResult.data);
                        populateAssignmentFilter(assignmentResult.data);
                    }
                }

                // Load detailed submissions
                loadDetailedSubmissions();

            } catch (error) {
                console.error('Error loading detailed reports:', error);
            }
        }

        function displayAssignmentStatistics(assignments) {
            const tbody = document.getElementById('assignment-stats-tbody');

            if (assignments.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center">Chưa có dữ liệu</td></tr>';
                return;
            }

            const html = assignments.map(assignment => {
                const difficultyBadge = getDifficultyBadge(assignment.difficulty);
                const languageBadge = getLanguageBadge(assignment.language);

                return `
                    <tr>
                        <td><strong>${assignment.title}</strong></td>
                        <td>${languageBadge}</td>
                        <td>${difficultyBadge}</td>
                        <td>${formatDateTime(assignment.deadline)}</td>
                        <td><span class="badge bg-info">${assignment.submission_count}</span></td>
                        <td><span class="badge bg-primary">${assignment.avg_score}</span></td>
                        <td>${assignment.min_score}</td>
                        <td>${assignment.max_score}</td>
                    </tr>
                `;
            }).join('');

            tbody.innerHTML = html;
        }

        async function loadDetailedSubmissions(assignmentId = null) {
            try {
                const url = assignmentId ?
                    `/api/teacher/detailed-submissions?assignment_id=${assignmentId}` :
                    '/api/teacher/detailed-submissions';

                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const result = await response.json();

                if (result.success) {
                    displayDetailedSubmissions(result.data);
                }
            } catch (error) {
                console.error('Error loading detailed submissions:', error);
            }
        }

        function displayDetailedSubmissions(submissions) {
            const tbody = document.getElementById('detailed-submissions-tbody');

            if (submissions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center">Chưa có dữ liệu</td></tr>';
                return;
            }

            const html = submissions.map(submission => {
                const scoreClass = submission.score >= 80 ? 'text-success' :
                                 submission.score >= 60 ? 'text-warning' : 'text-danger';

                const difficultyBadge = getDifficultyBadge(submission.difficulty);
                const languageBadge = getLanguageBadge(submission.language);

                return `
                    <tr>
                        <td><strong>${submission.assignment_title}</strong></td>
                        <td>${submission.full_name}<br><small class="text-muted">${submission.username}</small></td>
                        <td><span class="${scoreClass}">${submission.score}</span></td>
                        <td>${formatDateTime(submission.submitted_at)}</td>
                        <td>${languageBadge}</td>
                        <td>${difficultyBadge}</td>
                        <td>${submission.feedback || 'Chưa có phản hồi'}</td>
                    </tr>
                `;
            }).join('');

            tbody.innerHTML = html;
        }

        function loadAssignmentsChart() {
            const ctx = document.getElementById('assignments-chart').getContext('2d');

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Python', 'Perl', 'Mixed'],
                    datasets: [{
                        data: [8, 5, 2],
                        backgroundColor: ['#667eea', '#764ba2', '#f093fb']
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function loadProgressChart() {
            const ctx = document.getElementById('progress-chart').getContext('2d');

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Tuần 1', 'Tuần 2', 'Tuần 3', 'Tuần 4', 'Tuần 5'],
                    datasets: [{
                        label: 'Số bài nộp',
                        data: [12, 19, 15, 25, 22],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function loadSubmissionsTable() {
            const submissions = [
                { student: 'Nguyễn Văn A', assignment: 'Hello World Python', score: 95, date: '2024-01-15' },
                { student: 'Trần Thị B', assignment: 'Vòng lặp cơ bản', score: 87, date: '2024-01-14' },
                { student: 'Lê Văn C', assignment: 'Hàm trong Python', score: 92, date: '2024-01-13' },
                { student: 'Phạm Thị D', assignment: 'Xử lý chuỗi', score: 78, date: '2024-01-12' },
                { student: 'Hoàng Văn E', assignment: 'List và Dictionary', score: 85, date: '2024-01-11' }
            ];

            const html = `
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Sinh viên</th>
                                <th>Bài tập</th>
                                <th>Điểm</th>
                                <th>Ngày nộp</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${submissions.map(sub => `
                                <tr>
                                    <td>${sub.student}</td>
                                    <td>${sub.assignment}</td>
                                    <td>
                                        <span class="badge ${sub.score >= 90 ? 'bg-success' : sub.score >= 80 ? 'bg-warning' : 'bg-danger'}">
                                            ${sub.score}%
                                        </span>
                                    </td>
                                    <td>${formatDate(sub.date)}</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('submissions-table').innerHTML = html;
        }

        // Assignment actions
        function viewAssignmentDetails(id) {
            alert(`Xem chi tiết bài tập #${id}`);
        }

        function viewSubmissions(id) {
            alert(`Xem bài nộp cho bài tập #${id}`);
        }

        function editAssignment(id) {
            alert(`Chỉnh sửa bài tập #${id}`);
        }

        function deleteAssignment(id) {
            if (confirm('Bạn có chắc chắn muốn xóa bài tập này?')) {
                alert(`Xóa bài tập #${id}`);
            }
        }

        // Utility functions
        function formatDate(dateString) {
            if (!dateString) return 'Không có';
            const date = new Date(dateString);
            return date.toLocaleDateString('vi-VN') + ' ' + date.toLocaleTimeString('vi-VN');
        }

        function showProfile() {
            alert('Chức năng hồ sơ đang được phát triển');
        }

        function showSettings() {
            alert('Chức năng cài đặt đang được phát triển');
        }

        function logout() {
            localStorage.removeItem('access_token');
            localStorage.removeItem('user_role');
            window.location.href = '/';
        }

        // Helper functions
        function getDifficultyBadge(difficulty) {
            const badges = {
                'easy': '<span class="badge bg-success">Dễ</span>',
                'medium': '<span class="badge bg-warning">Trung bình</span>',
                'hard': '<span class="badge bg-danger">Khó</span>'
            };
            return badges[difficulty] || `<span class="badge bg-secondary">${difficulty}</span>`;
        }

        function getLanguageBadge(language) {
            const badges = {
                'python': '<span class="badge bg-primary">Python</span>',
                'perl': '<span class="badge bg-info">Perl</span>',
                'javascript': '<span class="badge bg-warning">JavaScript</span>'
            };
            return badges[language] || `<span class="badge bg-secondary">${language}</span>`;
        }

        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return 'N/A';
            try {
                const date = new Date(dateTimeStr);
                return date.toLocaleString('vi-VN');
            } catch (e) {
                return dateTimeStr;
            }
        }

        // Export functions
        async function exportExcelReport() {
            try {
                const response = await fetch('/api/teacher/export-excel', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const result = await response.json();

                if (result.success) {
                    // Convert base64 to blob and download
                    const byteCharacters = atob(result.data);
                    const byteNumbers = new Array(byteCharacters.length);
                    for (let i = 0; i < byteCharacters.length; i++) {
                        byteNumbers[i] = byteCharacters.charCodeAt(i);
                    }
                    const byteArray = new Uint8Array(byteNumbers);
                    const blob = new Blob([byteArray], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = result.filename;
                    a.click();
                    window.URL.revokeObjectURL(url);

                    alert('Báo cáo Excel đã được tải xuống thành công!');
                } else {
                    alert('Lỗi khi xuất báo cáo Excel');
                }
            } catch (error) {
                console.error('Error exporting Excel report:', error);
                alert('Lỗi khi xuất báo cáo Excel');
            }
        }

        function exportPDFReport() {
            // PDF export functionality - would need additional library like jsPDF
            alert('Tính năng xuất PDF đang được phát triển');
        }
    </script>
</body>

</html>