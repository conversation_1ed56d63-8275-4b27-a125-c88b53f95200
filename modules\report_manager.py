"""
Report Manager - <PERSON><PERSON><PERSON><PERSON> lý báo cáo và thống kê cho giảng viên
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import pandas as pd
from io import BytesIO
import base64

class ReportManager:
    def __init__(self):
        self.db_name = "cs466_database.db"
    
    def get_teacher_statistics(self, teacher_id: int) -> Dict:
        """L<PERSON>y thống kê tổng quan cho giảng viên"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        # Tổng số bài tập
        cursor.execute('''
            SELECT COUNT(*) FROM assignments WHERE teacher_id = ?
        ''', (teacher_id,))
        total_assignments = cursor.fetchone()[0]
        
        # Tổng số sinh viên (unique students who submitted)
        cursor.execute('''
            SELECT COUNT(DISTINCT s.student_id)
            FROM submissions s
            JOIN assignments a ON s.assignment_id = a.id
            WHERE a.teacher_id = ?
        ''', (teacher_id,))
        total_students = cursor.fetchone()[0]
        
        # Tổng số bài nộp
        cursor.execute('''
            SELECT COUNT(*)
            FROM submissions s
            JOIN assignments a ON s.assignment_id = a.id
            WHERE a.teacher_id = ?
        ''', (teacher_id,))
        total_submissions = cursor.fetchone()[0]
        
        # Điểm trung bình
        cursor.execute('''
            SELECT AVG(s.score)
            FROM submissions s
            JOIN assignments a ON s.assignment_id = a.id
            WHERE a.teacher_id = ? AND s.score IS NOT NULL
        ''', (teacher_id,))
        avg_score = cursor.fetchone()[0] or 0
        
        # Số bài tập theo độ khó
        cursor.execute('''
            SELECT difficulty, COUNT(*)
            FROM assignments
            WHERE teacher_id = ?
            GROUP BY difficulty
        ''', (teacher_id,))
        difficulty_stats = dict(cursor.fetchall())
        
        # Số bài nộp theo ngôn ngữ
        cursor.execute('''
            SELECT a.language, COUNT(s.id)
            FROM assignments a
            LEFT JOIN submissions s ON a.id = s.assignment_id
            WHERE a.teacher_id = ?
            GROUP BY a.language
        ''', (teacher_id,))
        language_stats = dict(cursor.fetchall())
        
        conn.close()
        
        return {
            'total_assignments': total_assignments,
            'total_students': total_students,
            'total_submissions': total_submissions,
            'avg_score': round(avg_score, 2),
            'difficulty_stats': difficulty_stats,
            'language_stats': language_stats
        }
    
    def get_student_progress_report(self, teacher_id: int) -> List[Dict]:
        """Báo cáo tiến độ từng sinh viên"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT 
                u.id,
                u.username,
                u.full_name,
                COUNT(DISTINCT a.id) as total_assignments,
                COUNT(DISTINCT s.assignment_id) as completed_assignments,
                AVG(s.score) as avg_score,
                MAX(s.submitted_at) as last_submission
            FROM users u
            CROSS JOIN assignments a
            LEFT JOIN submissions s ON u.id = s.student_id AND a.id = s.assignment_id
            WHERE u.role = 'student' AND a.teacher_id = ?
            GROUP BY u.id, u.username, u.full_name
            ORDER BY avg_score DESC NULLS LAST
        ''', (teacher_id,))
        
        students = []
        for row in cursor.fetchall():
            student_id, username, full_name, total_assignments, completed, avg_score, last_submission = row
            
            progress_rate = (completed / total_assignments * 100) if total_assignments > 0 else 0
            
            students.append({
                'student_id': student_id,
                'username': username,
                'full_name': full_name or username,
                'total_assignments': total_assignments,
                'completed_assignments': completed,
                'progress_rate': round(progress_rate, 2),
                'avg_score': round(avg_score, 2) if avg_score else 0,
                'last_submission': last_submission
            })
        
        conn.close()
        return students
    
    def get_assignment_statistics(self, teacher_id: int) -> List[Dict]:
        """Thống kê từng bài tập"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT 
                a.id,
                a.title,
                a.language,
                a.difficulty,
                a.deadline,
                COUNT(s.id) as submission_count,
                AVG(s.score) as avg_score,
                MIN(s.score) as min_score,
                MAX(s.score) as max_score
            FROM assignments a
            LEFT JOIN submissions s ON a.id = s.assignment_id
            WHERE a.teacher_id = ?
            GROUP BY a.id, a.title, a.language, a.difficulty, a.deadline
            ORDER BY a.created_at DESC
        ''', (teacher_id,))
        
        assignments = []
        for row in cursor.fetchall():
            assignment_id, title, language, difficulty, deadline, submission_count, avg_score, min_score, max_score = row
            
            assignments.append({
                'assignment_id': assignment_id,
                'title': title,
                'language': language,
                'difficulty': difficulty,
                'deadline': deadline,
                'submission_count': submission_count,
                'avg_score': round(avg_score, 2) if avg_score else 0,
                'min_score': min_score or 0,
                'max_score': max_score or 0
            })
        
        conn.close()
        return assignments
    
    def get_submission_timeline(self, teacher_id: int, days: int = 30) -> List[Dict]:
        """Thống kê bài nộp theo thời gian"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT 
                DATE(s.submitted_at) as date,
                COUNT(s.id) as submission_count,
                AVG(s.score) as avg_score
            FROM submissions s
            JOIN assignments a ON s.assignment_id = a.id
            WHERE a.teacher_id = ? 
            AND s.submitted_at >= datetime('now', '-{} days')
            GROUP BY DATE(s.submitted_at)
            ORDER BY date
        '''.format(days), (teacher_id,))
        
        timeline = []
        for row in cursor.fetchall():
            date, submission_count, avg_score = row
            timeline.append({
                'date': date,
                'submission_count': submission_count,
                'avg_score': round(avg_score, 2) if avg_score else 0
            })
        
        conn.close()
        return timeline
    
    def get_detailed_submissions(self, teacher_id: int, assignment_id: Optional[int] = None) -> List[Dict]:
        """Lấy chi tiết tất cả bài nộp"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        query = '''
            SELECT 
                s.id,
                a.title as assignment_title,
                u.username,
                u.full_name,
                s.score,
                s.submitted_at,
                s.feedback,
                a.language,
                a.difficulty
            FROM submissions s
            JOIN assignments a ON s.assignment_id = a.id
            JOIN users u ON s.student_id = u.id
            WHERE a.teacher_id = ?
        '''
        
        params = [teacher_id]
        
        if assignment_id:
            query += ' AND a.id = ?'
            params.append(assignment_id)
        
        query += ' ORDER BY s.submitted_at DESC'
        
        cursor.execute(query, params)
        
        submissions = []
        for row in cursor.fetchall():
            submission_id, assignment_title, username, full_name, score, submitted_at, feedback, language, difficulty = row
            
            submissions.append({
                'submission_id': submission_id,
                'assignment_title': assignment_title,
                'username': username,
                'full_name': full_name or username,
                'score': score or 0,
                'submitted_at': submitted_at,
                'feedback': feedback,
                'language': language,
                'difficulty': difficulty
            })
        
        conn.close()
        return submissions
    
    def generate_excel_report(self, teacher_id: int) -> bytes:
        """Tạo báo cáo Excel"""
        try:
            # Lấy dữ liệu
            statistics = self.get_teacher_statistics(teacher_id)
            student_progress = self.get_student_progress_report(teacher_id)
            assignment_stats = self.get_assignment_statistics(teacher_id)
            submissions = self.get_detailed_submissions(teacher_id)
            
            # Tạo Excel file
            output = BytesIO()
            
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                # Sheet 1: Tổng quan
                overview_data = {
                    'Chỉ số': ['Tổng số bài tập', 'Tổng số sinh viên', 'Tổng số bài nộp', 'Điểm trung bình'],
                    'Giá trị': [
                        statistics['total_assignments'],
                        statistics['total_students'], 
                        statistics['total_submissions'],
                        f"{statistics['avg_score']}%"
                    ]
                }
                pd.DataFrame(overview_data).to_excel(writer, sheet_name='Tổng quan', index=False)
                
                # Sheet 2: Tiến độ sinh viên
                if student_progress:
                    df_students = pd.DataFrame(student_progress)
                    df_students.to_excel(writer, sheet_name='Tiến độ sinh viên', index=False)
                
                # Sheet 3: Thống kê bài tập
                if assignment_stats:
                    df_assignments = pd.DataFrame(assignment_stats)
                    df_assignments.to_excel(writer, sheet_name='Thống kê bài tập', index=False)
                
                # Sheet 4: Chi tiết bài nộp
                if submissions:
                    df_submissions = pd.DataFrame(submissions)
                    df_submissions.to_excel(writer, sheet_name='Chi tiết bài nộp', index=False)
            
            output.seek(0)
            return output.getvalue()
            
        except Exception as e:
            print(f"Error generating Excel report: {e}")
            return b""
    
    def get_grade_distribution(self, teacher_id: int, assignment_id: Optional[int] = None) -> Dict:
        """Phân bố điểm số"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        query = '''
            SELECT 
                CASE 
                    WHEN s.score >= 90 THEN 'A (90-100)'
                    WHEN s.score >= 80 THEN 'B (80-89)'
                    WHEN s.score >= 70 THEN 'C (70-79)'
                    WHEN s.score >= 60 THEN 'D (60-69)'
                    ELSE 'F (0-59)'
                END as grade_range,
                COUNT(*) as count
            FROM submissions s
            JOIN assignments a ON s.assignment_id = a.id
            WHERE a.teacher_id = ? AND s.score IS NOT NULL
        '''
        
        params = [teacher_id]
        
        if assignment_id:
            query += ' AND a.id = ?'
            params.append(assignment_id)
        
        query += ' GROUP BY grade_range ORDER BY MIN(s.score) DESC'
        
        cursor.execute(query, params)
        
        distribution = {}
        for grade_range, count in cursor.fetchall():
            distribution[grade_range] = count
        
        conn.close()
        return distribution
