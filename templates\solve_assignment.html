<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Làm bài: {{ assignment.title }} - CS466</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            color: white !important;
            font-weight: bold;
        }
        
        .assignment-header {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .assignment-title {
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .assignment-meta {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }
        
        .meta-item {
            background: #f8f9fa;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
        }
        
        .deadline-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .deadline-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .coding-workspace {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .workspace-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        
        .workspace-tools {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        
        .workspace-body {
            display: flex;
            height: 600px;
        }
        
        .code-panel {
            flex: 1;
            border-right: 1px solid #e9ecef;
        }
        
        .output-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .panel-header {
            background: #f8f9fa;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #495057;
        }
        
        .code-editor {
            height: 100%;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
        
        .output-content {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background: #f8f9fa;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
        }
        
        .test-results {
            border-top: 1px solid #e9ecef;
            padding: 1rem;
            background: white;
        }
        
        .test-case {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-left: 4px solid #6c757d;
        }
        
        .test-case.passed {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .test-case.failed {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .submission-panel {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-top: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .btn-run {
            background: #28a745;
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-run:hover {
            background: #218838;
            transform: translateY(-1px);
        }
        
        .btn-submit {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s;
        }
        
        .btn-submit:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
        }
        
        .time-remaining {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 0.5rem 1rem;
            color: #856404;
            font-weight: 600;
        }
        
        .progress-bar-custom {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s;
        }
        
        .quiz-container {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .question {
            margin-bottom: 2rem;
        }
        
        .question-number {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
        }
        
        .option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .option:hover {
            background: #e9ecef;
            border-color: #667eea;
        }
        
        .option.selected {
            background: rgba(102, 126, 234, 0.1);
            border-color: #667eea;
        }
        
        .file-upload-area {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 3rem;
            text-align: center;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .file-upload-area:hover {
            background: rgba(102, 126, 234, 0.05);
            border-color: #5a6fd8;
        }
        
        .file-upload-area.dragover {
            background: rgba(102, 126, 234, 0.1);
            border-color: #5a6fd8;
        }
        
        .uploaded-file {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 1rem;
            margin-top: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        @media (max-width: 768px) {
            .workspace-body {
                flex-direction: column;
                height: auto;
            }
            
            .code-panel {
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }
            
            .code-editor {
                height: 400px;
            }
            
            .output-panel {
                height: 300px;
            }
            
            .assignment-meta {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard/student">
                <i class="fas fa-arrow-left"></i> Quay lại Dashboard
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="time-remaining">
                    <i class="fas fa-clock"></i> <span id="time-remaining">Đang tính...</span>
                </div>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid">
        <!-- Assignment Header -->
        <div class="assignment-header">
            <h1 class="assignment-title">{{ assignment.title }}</h1>
            <div class="assignment-meta">
                <span class="meta-item">
                    <i class="fas fa-code"></i> {{ assignment.language }}
                </span>
                <span class="meta-item">
                    <i class="fas fa-layer-group"></i> {{ assignment.assignment_type }}
                </span>
                <span class="meta-item">
                    <i class="fas fa-user"></i> {{ assignment.teacher_name }}
                </span>
                <span class="meta-item deadline-warning">
                    <i class="fas fa-calendar"></i> Deadline: {{ assignment.deadline }}
                </span>
            </div>
            <p class="assignment-description">{{ assignment.description }}</p>
            
            <!-- Progress Bar -->
            <div class="progress-bar-custom">
                <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
            </div>
            <small class="text-muted">Tiến độ hoàn thành</small>
        </div>
        
        <!-- Assignment Content Based on Type -->
        {% if assignment.assignment_type == 'code' %}
        <!-- Coding Assignment -->
        <div class="coding-workspace">
            <div class="workspace-header">
                <h5><i class="fas fa-code"></i> Code Editor</h5>
                <div class="workspace-tools">
                    <select class="form-select form-select-sm me-2" id="language-select" disabled>
                        <option value="{{ assignment.language }}">{{ assignment.language.title() }}</option>
                    </select>
                    <div class="btn-group btn-group-sm me-2">
                        <button class="btn btn-run" onclick="runCode()">
                            <i class="fas fa-play"></i> Run
                        </button>
                        <button class="btn btn-success" onclick="runInteractiveMode()">
                            <i class="fas fa-keyboard"></i> Interactive
                        </button>
                    </div>
                    <div class="btn-group btn-group-sm me-2">
                        <button class="btn btn-secondary" onclick="resetCode()">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                        <button class="btn btn-outline-secondary" onclick="downloadCode()">
                            <i class="fas fa-download"></i> Download
                        </button>
                        <button class="btn btn-outline-info" onclick="shareCode()">
                            <i class="fas fa-share"></i> Share
                        </button>
                    </div>
                    <button class="btn btn-info btn-sm" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i> Fullscreen
                    </button>
                </div>
            </div>
            
            <div class="workspace-body">
                <div class="code-panel">
                    <div class="panel-header">
                        <i class="fas fa-edit"></i> Code Editor
                    </div>
                    <div id="monaco-editor" class="code-editor"></div>
                </div>
                
                <div class="output-panel">
                    <div class="panel-header">
                        <i class="fas fa-terminal"></i> Output & Results
                    </div>
                    <div class="output-content" id="output-content">
                        <em>Kết quả sẽ hiển thị ở đây sau khi chạy code...</em>
                    </div>
                    
                    {% if assignment.test_cases %}
                    <div class="test-results">
                        <h6><i class="fas fa-flask"></i> Test Cases</h6>
                        <div id="test-results"></div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        {% elif assignment.assignment_type == 'quiz' %}
        <!-- Quiz Assignment -->
        <div class="quiz-container">
            <h4><i class="fas fa-question-circle"></i> Câu hỏi trắc nghiệm</h4>
            <div id="quiz-questions"></div>
        </div>
        
        {% elif assignment.assignment_type == 'file' %}
        <!-- File Upload Assignment -->
        <div class="quiz-container">
            <h4><i class="fas fa-upload"></i> Upload File</h4>
            <div class="file-upload-area" id="file-upload-area">
                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                <h5>Kéo thả file vào đây hoặc click để chọn</h5>
                <p class="text-muted">Hỗ trợ các định dạng: .py, .pl, .txt, .zip</p>
                <input type="file" id="file-input" class="d-none" accept=".py,.pl,.txt,.zip">
            </div>
            <div id="uploaded-files"></div>
        </div>
        {% endif %}
        
        <!-- Submission Panel -->
        <div class="submission-panel">
            <div class="row">
                <div class="col-md-8">
                    <h5><i class="fas fa-paper-plane"></i> Nộp bài</h5>
                    <p class="text-muted">Kiểm tra kỹ bài làm trước khi nộp. Bạn có thể nộp lại nhiều lần.</p>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirm-submit">
                        <label class="form-check-label" for="confirm-submit">
                            Tôi đã kiểm tra và chắc chắn muốn nộp bài này
                        </label>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-submit" onclick="submitAssignment()" id="submit-btn" disabled>
                        <i class="fas fa-paper-plane"></i> Nộp bài
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Monaco Editor -->
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.34.0/min/vs/loader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let editor;
        let assignment = {{ assignment | tojson }};
        let submissionData = {};
        const token = localStorage.getItem('access_token');
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            if (!token) {
                window.location.href = '/';
                return;
            }
            
            initializeAssignment();
            updateTimeRemaining();
            setInterval(updateTimeRemaining, 1000);
        });
        
        function initializeAssignment() {
            if (assignment.assignment_type === 'code') {
                initializeCodeEditor();
            } else if (assignment.assignment_type === 'quiz') {
                initializeQuiz();
            } else if (assignment.assignment_type === 'file') {
                initializeFileUpload();
            }
            
            // Enable submit button when confirm checkbox is checked
            document.getElementById('confirm-submit').addEventListener('change', function() {
                document.getElementById('submit-btn').disabled = !this.checked;
            });
        }
        
        // Code Editor Functions
        function initializeCodeEditor() {
            require.config({ paths: { vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.34.0/min/vs' } });
            
            require(['vs/editor/editor.main'], function () {
                editor = monaco.editor.create(document.getElementById('monaco-editor'), {
                    value: getStarterCode(),
                    language: assignment.language === 'python' ? 'python' : 'perl',
                    theme: 'vs-dark',
                    fontSize: 14,
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false,
                    automaticLayout: true,
                    wordWrap: 'on'
                });
                
                // Auto-save every 30 seconds
                setInterval(autoSave, 30000);
                
                // Save on editor change
                editor.onDidChangeModelContent(() => {
                    updateProgress();
                });
            });
        }
        
        function getStarterCode() {
            if (assignment.language === 'python') {
                return `# ${assignment.title}
# ${assignment.description}

def main():
    # Write your code here
    # NOTE: Do not use input() in web environment
    # Instead, use fixed values or lists for testing
    
    # Example: Instead of x = int(input("Enter number:"))
    # Use: x = 5  # or test_values = [1, 2, 3, 4, 5]
    
    pass

if __name__ == "__main__":
    main()`;
            } else {
                return `#!/usr/bin/perl
# ${assignment.title}
# ${assignment.description}

use strict;
use warnings;

# Write your code here
# NOTE: Do not use input in web environment

`;
            }
        }
        
        async function runCode() {
            if (!editor) return;
            
            const code = editor.getValue();
            const outputDiv = document.getElementById('output-content');
            
            if (!code.trim()) {
                outputDiv.innerHTML = '<span class="text-danger">Please enter code before running!</span>';
                return;
            }
            
            // Show loading with timeout indicator
            outputDiv.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    <span>Running code... (max 3s)</span>
                </div>
            `;
            
            const startTime = Date.now();
            
            try {
                const formData = new FormData();
                formData.append('code', code);
                formData.append('language', assignment.language);
                
                // Add timeout to fetch request
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 8000); // 8s client timeout
                
                const response = await fetch('/code/run', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData,
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                const endTime = Date.now();
                const executionTime = endTime - startTime;
                
                const result = await response.json();
                
                if (result.success) {
                    displayCodeOutput(result.result, executionTime);
                    if (assignment.test_cases) {
                        runTestCases(code);
                    }
                } else {
                    outputDiv.innerHTML = `<span class="text-danger">Error: ${result.result?.error || 'Unknown error'}</span>`;
                }
            } catch (error) {
                if (error.name === 'AbortError') {
                    outputDiv.innerHTML = '<span class="text-danger">Request timeout - Code took too long to execute</span>';
                } else {
                    outputDiv.innerHTML = `<span class="text-danger">Connection error: ${error.message}</span>`;
                }
            }
        }
        
        function displayCodeOutput(result, executionTime = 0) {
            const outputDiv = document.getElementById('output-content');
            
            let html = '';
            
            // Add execution time info
            html += `
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-clock"></i> Execution time: ${executionTime}ms
                        <span class="badge ${result.return_code === 0 ? 'bg-success' : 'bg-danger'} ms-2">
                            Code: ${result.return_code}
                        </span>
                    </small>
                </div>
            `;
            
            if (result.output) {
                html += `
                    <div class="mb-3">
                        <strong class="text-success">Output:</strong>
                        <pre class="bg-dark text-light p-2 rounded mt-1 small">${result.output}</pre>
                    </div>
                `;
            }
            
            if (result.error) {
                html += `
                    <div class="mb-3">
                        <strong class="text-danger">Error:</strong>
                        <pre class="bg-danger text-white p-2 rounded mt-1 small">${result.error}</pre>
                    </div>
                `;
            }
            
            if (!result.output && !result.error) {
                html += `
                    <div class="text-muted">
                        <i class="fas fa-info-circle"></i> No output generated
                    </div>
                `;
            }
            
            outputDiv.innerHTML = html;
        }
        
        function runTestCases(code) {
            const testCases = assignment.test_cases;
            const testResultsDiv = document.getElementById('test-results');
            
            let passedCount = 0;
            let testResults = '';
            
            testCases.forEach((testCase, index) => {
                // Mock test execution - in real implementation, this would run the code with test inputs
                const passed = Math.random() > 0.3; // Simulate 70% pass rate
                if (passed) passedCount++;
                
                testResults += `
                    <div class="test-case ${passed ? 'passed' : 'failed'}">
                        <div class="d-flex justify-content-between align-items-center">
                            <strong>Test Case ${index + 1}</strong>
                            <span class="badge ${passed ? 'bg-success' : 'bg-danger'}">
                                ${passed ? 'PASSED' : 'FAILED'}
                            </span>
                        </div>
                        <div class="mt-2">
                            <small><strong>Input:</strong> ${testCase.input || 'No input'}</small><br>
                            <small><strong>Expected:</strong> ${testCase.expected_output}</small><br>
                            ${!passed ? '<small class="text-danger"><strong>Got:</strong> Wrong output</small>' : ''}
                        </div>
                    </div>
                `;
            });
            
            testResultsDiv.innerHTML = `
                <div class="mb-2">
                    <strong>Test Results: ${passedCount}/${testCases.length} passed</strong>
                </div>
                ${testResults}
            `;
        }
        
        function resetCode() {
            if (editor) {
                editor.setValue(getStarterCode());
                document.getElementById('output-content').innerHTML = '<em>Kết quả sẽ hiển thị ở đây sau khi chạy code...</em>';
                document.getElementById('test-results').innerHTML = '';
            }
        }
        
        function toggleFullscreen() {
            const workspace = document.querySelector('.coding-workspace');
            if (workspace.requestFullscreen) {
                workspace.requestFullscreen();
            }
        }
        
        // Quiz Functions
        function initializeQuiz() {
            const questionsDiv = document.getElementById('quiz-questions');
            const questions = assignment.test_cases; // Quiz questions stored in test_cases
            
            let html = '';
            questions.forEach((question, qIndex) => {
                html += `
                    <div class="question">
                        <div class="d-flex align-items-start mb-3">
                            <span class="question-number">${qIndex + 1}</span>
                            <h6>${question.question}</h6>
                        </div>
                        <div class="options">
                            ${question.options.map((option, oIndex) => `
                                <div class="option" onclick="selectOption(${qIndex}, ${oIndex})" data-question="${qIndex}" data-option="${oIndex}">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="question_${qIndex}" value="${oIndex}">
                                        <label class="form-check-label">
                                            ${String.fromCharCode(65 + oIndex)}. ${option}
                                        </label>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            });
            
            questionsDiv.innerHTML = html;
            submissionData.answers = new Array(questions.length);
        }
        
        function selectOption(questionIndex, optionIndex) {
            // Remove previous selection
            document.querySelectorAll(`[data-question="${questionIndex}"]`).forEach(el => {
                el.classList.remove('selected');
            });
            
            // Add selection to clicked option
            const selectedOption = document.querySelector(`[data-question="${questionIndex}"][data-option="${optionIndex}"]`);
            selectedOption.classList.add('selected');
            
            // Update radio button
            const radio = selectedOption.querySelector('input[type="radio"]');
            radio.checked = true;
            
            // Store answer
            submissionData.answers[questionIndex] = optionIndex;
            updateProgress();
        }
        
        // File Upload Functions
        function initializeFileUpload() {
            const uploadArea = document.getElementById('file-upload-area');
            const fileInput = document.getElementById('file-input');
            
            uploadArea.addEventListener('click', () => fileInput.click());
            
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                handleFiles(e.dataTransfer.files);
            });
            
            fileInput.addEventListener('change', (e) => {
                handleFiles(e.target.files);
            });
        }
        
        function handleFiles(files) {
            const uploadedFilesDiv = document.getElementById('uploaded-files');
            
            Array.from(files).forEach(file => {
                const fileDiv = document.createElement('div');
                fileDiv.className = 'uploaded-file';
                fileDiv.innerHTML = `
                    <div>
                        <i class="fas fa-file"></i> ${file.name} (${formatFileSize(file.size)})
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="removeFile(this)">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                uploadedFilesDiv.appendChild(fileDiv);
            });
            
            submissionData.files = files;
            updateProgress();
        }
        
        function removeFile(button) {
            button.parentElement.remove();
            updateProgress();
        }
        
        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
            return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
        }
        
        // Progress and Submission Functions
        function updateProgress() {
            let progress = 0;
            
            if (assignment.assignment_type === 'code') {
                const code = editor ? editor.getValue().trim() : '';
                progress = Math.min(100, (code.length / 100) * 100); // Simple progress based on code length
            } else if (assignment.assignment_type === 'quiz') {
                const answered = submissionData.answers ? submissionData.answers.filter(a => a !== undefined).length : 0;
                const total = assignment.test_cases.length;
                progress = (answered / total) * 100;
            } else if (assignment.assignment_type === 'file') {
                progress = submissionData.files && submissionData.files.length > 0 ? 100 : 0;
            }
            
            document.getElementById('progress-fill').style.width = progress + '%';
        }
        
        async function submitAssignment() {
            const submitBtn = document.getElementById('submit-btn');
            const originalText = submitBtn.innerHTML;
            
            // Validation before submit
            if (assignment.assignment_type === 'code') {
                const code = editor.getValue().trim();
                if (!code) {
                    alert('Vui lòng nhập code trước khi nộp bài!');
                    return;
                }
            } else if (assignment.assignment_type === 'quiz') {
                const totalQuestions = assignment.test_cases.length;
                const answeredQuestions = submissionData.answers ? submissionData.answers.filter(a => a !== undefined).length : 0;
                
                if (answeredQuestions < totalQuestions) {
                    const confirm = window.confirm(`Bạn chưa trả lời ${totalQuestions - answeredQuestions} câu hỏi. Bạn có muốn nộp bài không?`);
                    if (!confirm) return;
                }
            } else if (assignment.assignment_type === 'file') {
                if (!submissionData.files || submissionData.files.length === 0) {
                    alert('Vui lòng chọn ít nhất 1 file để nộp bài!');
                    return;
                }
            }
            
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang nộp...';
            
            try {
                const formData = new FormData();
                
                if (assignment.assignment_type === 'code') {
                    formData.append('code', editor.getValue());
                } else if (assignment.assignment_type === 'quiz') {
                    formData.append('answers', JSON.stringify(submissionData.answers));
                } else if (assignment.assignment_type === 'file') {
                    if (submissionData.files) {
                        Array.from(submissionData.files).forEach(file => {
                            formData.append('files', file);
                        });
                    }
                }
                
                const response = await fetch(`/assignment/${assignment.id}/submit`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });
                
                if (response.ok) {
                    const result = await response.json();
                    
                    // Clean up auto-saved data on successful submission
                    cleanupAutoSave();
                    
                    // Show success message with better UX
                    const successMsg = document.createElement('div');
                    successMsg.className = 'alert alert-success alert-dismissible fade show position-fixed';
                    successMsg.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                    successMsg.innerHTML = `
                        <i class="fas fa-check-circle"></i> <strong>Nộp bài thành công!</strong>
                        <p class="mb-0">Bạn có thể xem kết quả trong dashboard.</p>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(successMsg);
                    
                    // Auto redirect after 2 seconds
                    setTimeout(() => {
                        window.location.href = '/dashboard/student';
                    }, 2000);
                } else {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || 'Lỗi không xác định');
                }
            } catch (error) {
                console.error('Submission error:', error);
                
                // Show detailed error message
                const errorMsg = document.createElement('div');
                errorMsg.className = 'alert alert-danger alert-dismissible fade show position-fixed';
                errorMsg.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                errorMsg.innerHTML = `
                    <i class="fas fa-exclamation-triangle"></i> <strong>Lỗi nộp bài!</strong>
                    <p class="mb-1">${error.message}</p>
                    <small>Vui lòng kiểm tra kết nối internet và thử lại.</small>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.appendChild(errorMsg);
                
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        }
        
        function autoSave() {
            // Auto-save functionality - save to localStorage
            if (assignment.assignment_type === 'code' && editor) {
                localStorage.setItem(`assignment_${assignment.id}_code`, editor.getValue());
            } else if (assignment.assignment_type === 'quiz' && submissionData.answers) {
                localStorage.setItem(`assignment_${assignment.id}_answers`, JSON.stringify(submissionData.answers));
            }
        }
        
        // Interactive Mode Functions - Similar to online-python.com
        function runInteractiveMode() {
            if (!editor) return;
            
            const code = editor.getValue();
            if (!code.trim()) {
                showToast('Please enter code before running!', 'warning');
                return;
            }
            
            // Check if code has input() calls
            if (code.includes('input(')) {
                showInteractiveDialog(code);
            } else {
                // Regular execution
                runCode();
            }
        }
        
        function showInteractiveDialog(code) {
            const dialogHTML = `
                <div class="modal fade" id="interactiveModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-keyboard"></i> Interactive Mode
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p>Your code contains input() calls. Please provide the input values:</p>
                                <div class="mb-3">
                                    <label class="form-label">Input Values (one per line):</label>
                                    <textarea class="form-control" id="inputValues" rows="5" 
                                              placeholder="Enter each input value on a new line&#10;Example:&#10;5&#10;Hello World&#10;42"></textarea>
                                </div>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> 
                                    Each line will be used as an input value when your code calls input()
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-success" onclick="executeInteractiveCode()">
                                    <i class="fas fa-play"></i> Run Interactive
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Add modal to DOM
            const existingModal = document.getElementById('interactiveModal');
            if (existingModal) existingModal.remove();
            
            document.body.insertAdjacentHTML('beforeend', dialogHTML);
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('interactiveModal'));
            modal.show();
        }
        
        async function executeInteractiveCode() {
            const code = editor.getValue();
            const inputValues = document.getElementById('inputValues').value.trim();
            const inputs = inputValues ? inputValues.split('\n') : [];
            
            const outputDiv = document.getElementById('output-content');
            outputDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Executing interactive code...';
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('interactiveModal'));
            modal.hide();
            
            try {
                const formData = new FormData();
                formData.append('code', code);
                formData.append('inputs', JSON.stringify(inputs));
                
                const response = await fetch('/code/run-interactive', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    let outputHTML = '';
                    
                    if (result.result.output) {
                        outputHTML += `<div class="output-section">
                            <h6><i class="fas fa-terminal"></i> Output:</h6>
                            <pre class="code-output success">${escapeHtml(result.result.output)}</pre>
                        </div>`;
                    }
                    
                    if (result.result.error) {
                        outputHTML += `<div class="output-section">
                            <h6><i class="fas fa-exclamation-triangle"></i> Errors:</h6>
                            <pre class="code-output error">${escapeHtml(result.result.error)}</pre>
                        </div>`;
                    }
                    
                    if (result.result.needs_input) {
                        outputHTML += `<div class="alert alert-warning">
                            <i class="fas fa-info-circle"></i> 
                            Program requires more input. Please provide additional values and try again.
                        </div>`;
                    }
                    
                    outputDiv.innerHTML = outputHTML || '<em>No output</em>';
                } else {
                    outputDiv.innerHTML = `<div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> 
                        Error: ${result.result.error || 'Unknown error'}
                    </div>`;
                }
                
            } catch (error) {
                console.error('Interactive execution error:', error);
                outputDiv.innerHTML = `<div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> 
                    Network error: ${error.message}
                </div>`;
            }
        }
        
        // Download Code Function
        function downloadCode() {
            if (!editor) return;
            
            const code = editor.getValue();
            if (!code.trim()) {
                showToast('No code to download!', 'warning');
                return;
            }
            
            const blob = new Blob([code], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${assignment.title.replace(/[^a-zA-Z0-9]/g, '_')}.${assignment.language === 'python' ? 'py' : 'pl'}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showToast('Code downloaded successfully!', 'success');
        }
        
        // Share Code Function
        function shareCode() {
            if (!editor) return;
            
            const code = editor.getValue();
            if (!code.trim()) {
                showToast('No code to share!', 'warning');
                return;
            }
            
            // Create share URL (could be expanded to actually save and share)
            const shareData = {
                title: assignment.title,
                code: code,
                language: assignment.language,
                timestamp: new Date().toISOString()
            };
            
            const shareURL = `${window.location.origin}/shared/${btoa(JSON.stringify(shareData))}`;
            
            // Copy to clipboard
            navigator.clipboard.writeText(shareURL).then(() => {
                showToast('Share URL copied to clipboard!', 'success');
            }).catch(() => {
                // Fallback for older browsers
                const textarea = document.createElement('textarea');
                textarea.value = shareURL;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                showToast('Share URL copied to clipboard!', 'success');
            });
        }
        
        // Utility Functions
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(toast);
            
            // Auto remove after 3 seconds
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function updateTimeRemaining() {
            const deadline = new Date(assignment.deadline);
            const now = new Date();
            const timeLeft = deadline - now;
            
            const timeRemainingElement = document.getElementById('time-remaining');
            
            if (timeLeft <= 0) {
                timeRemainingElement.innerHTML = '<span class="text-danger">Đã hết hạn</span>';
                document.getElementById('submit-btn').disabled = true;
                return;
            }
            
            const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
            
            let timeString = '';
            if (days > 0) timeString += `${days}d `;
            if (hours > 0) timeString += `${hours}h `;
            if (minutes > 0) timeString += `${minutes}m `;
            timeString += `${seconds}s`;
            
            timeRemainingElement.textContent = timeString;
            
            // Change color based on time remaining
            if (timeLeft < 60000) { // Less than 1 minute
                timeRemainingElement.className = 'time-remaining text-danger';
            } else if (timeLeft < 300000) { // Less than 5 minutes
                timeRemainingElement.className = 'time-remaining text-warning';
            }
        }
        
        // Load auto-saved data
        window.addEventListener('load', function() {
            if (assignment.assignment_type === 'code') {
                const savedCode = localStorage.getItem(`assignment_${assignment.id}_code`);
                if (savedCode && editor) {
                    editor.setValue(savedCode);
                }
            } else if (assignment.assignment_type === 'quiz') {
                const savedAnswers = localStorage.getItem(`assignment_${assignment.id}_answers`);
                if (savedAnswers) {
                    submissionData.answers = JSON.parse(savedAnswers);
                    // Restore selections
                    submissionData.answers.forEach((answer, qIndex) => {
                        if (answer !== undefined) {
                            selectOption(qIndex, answer);
                        }
                    });
                }
            }
        });
        
        // Clean up auto-saved data on successful submission
        function cleanupAutoSave() {
            localStorage.removeItem(`assignment_${assignment.id}_code`);
            localStorage.removeItem(`assignment_${assignment.id}_answers`);
        }
    </script>
</body>
</html> 