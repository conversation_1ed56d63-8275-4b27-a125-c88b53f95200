"""
Demo script để tạo dữ liệu mẫu cho hệ thống AI Assessment
"""

import sqlite3
from datetime import datetime, timedelta
import random

def create_demo_data():
    """Tạo dữ liệu mẫu cho demo"""
    conn = sqlite3.connect("cs466_database.db")
    cursor = conn.cursor()
    
    # Tạ<PERSON> thêm một số bài tập mẫu
    demo_assignments = [
        {
            'title': 'Python Cơ Bản - Variables',
            'description': 'Học về biến và kiểu dữ liệu trong Python',
            'language': 'python',
            'difficulty': 'easy',
            'assignment_type': 'code',
            'teacher_id': 1,
            'deadline': (datetime.now() + timedelta(days=7)).isoformat()
        },
        {
            'title': 'Python - Vòng lặp For',
            'description': 'Thực hành với vòng lặp for trong Python',
            'language': 'python',
            'difficulty': 'easy',
            'assignment_type': 'code',
            'teacher_id': 1,
            'deadline': (datetime.now() + timedelta(days=5)).isoformat()
        },
        {
            'title': 'Python - Functions',
            'description': 'Tạo và sử dụng functions trong Python',
            'language': 'python',
            'difficulty': 'medium',
            'assignment_type': 'code',
            'teacher_id': 1,
            'deadline': (datetime.now() + timedelta(days=10)).isoformat()
        },
        {
            'title': 'Python - OOP Basics',
            'description': 'Lập trình hướng đối tượng cơ bản',
            'language': 'python',
            'difficulty': 'hard',
            'assignment_type': 'code',
            'teacher_id': 1,
            'deadline': (datetime.now() + timedelta(days=14)).isoformat()
        },
        {
            'title': 'Perl - Variables và Arrays',
            'description': 'Học về biến và mảng trong Perl',
            'language': 'perl',
            'difficulty': 'easy',
            'assignment_type': 'code',
            'teacher_id': 1,
            'deadline': (datetime.now() + timedelta(days=8)).isoformat()
        },
        {
            'title': 'Perl - Regular Expressions',
            'description': 'Sử dụng regex trong Perl',
            'language': 'perl',
            'difficulty': 'medium',
            'assignment_type': 'code',
            'teacher_id': 1,
            'deadline': (datetime.now() + timedelta(days=12)).isoformat()
        }
    ]
    
    # Thêm bài tập vào database
    for assignment in demo_assignments:
        cursor.execute('''
            INSERT OR IGNORE INTO assignments 
            (title, description, language, difficulty, assignment_type, teacher_id, deadline, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            assignment['title'],
            assignment['description'],
            assignment['language'],
            assignment['difficulty'],
            assignment['assignment_type'],
            assignment['teacher_id'],
            assignment['deadline'],
            datetime.now().isoformat()
        ))
    
    # Lấy danh sách assignment IDs
    cursor.execute('SELECT id FROM assignments')
    assignment_ids = [row[0] for row in cursor.fetchall()]
    
    # Lấy danh sách student IDs
    cursor.execute("SELECT id FROM users WHERE role = 'student'")
    student_ids = [row[0] for row in cursor.fetchall()]
    
    # Tạo submissions mẫu với điểm số đa dạng
    for student_id in student_ids:
        # Mỗi sinh viên làm một số bài tập ngẫu nhiên
        num_assignments = random.randint(3, len(assignment_ids))
        selected_assignments = random.sample(assignment_ids, num_assignments)
        
        for assignment_id in selected_assignments:
            # Tạo điểm số dựa trên "năng lực" của sinh viên
            if student_id == 2:  # student1 - giỏi
                base_score = random.randint(80, 100)
            elif student_id == 3:  # student2 - trung bình
                base_score = random.randint(60, 85)
            else:  # các sinh viên khác - yếu hơn
                base_score = random.randint(40, 75)
            
            # Thêm một chút biến động
            score = max(0, min(100, base_score + random.randint(-10, 10)))
            
            # Tạo submission
            cursor.execute('''
                INSERT OR IGNORE INTO submissions 
                (assignment_id, student_id, score, submitted_at, feedback)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                assignment_id,
                student_id,
                score,
                (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                f"Điểm: {score}/100. " + ("Làm tốt!" if score >= 80 else "Cần cải thiện." if score >= 60 else "Cần ôn tập thêm.")
            ))
    
    conn.commit()
    conn.close()
    print("✅ Đã tạo dữ liệu demo thành công!")
    print("📊 Dữ liệu bao gồm:")
    print("   - 6 bài tập mẫu (Python & Perl)")
    print("   - Submissions với điểm số đa dạng")
    print("   - Dữ liệu để test AI Assessment")

def show_demo_stats():
    """Hiển thị thống kê dữ liệu demo"""
    conn = sqlite3.connect("cs466_database.db")
    cursor = conn.cursor()
    
    print("\n📈 THỐNG KÊ DỮ LIỆU DEMO:")
    print("=" * 50)
    
    # Thống kê assignments
    cursor.execute('SELECT COUNT(*) FROM assignments')
    total_assignments = cursor.fetchone()[0]
    print(f"📝 Tổng số bài tập: {total_assignments}")
    
    cursor.execute('SELECT language, COUNT(*) FROM assignments GROUP BY language')
    for lang, count in cursor.fetchall():
        print(f"   - {lang}: {count} bài")
    
    # Thống kê submissions
    cursor.execute('SELECT COUNT(*) FROM submissions')
    total_submissions = cursor.fetchone()[0]
    print(f"📤 Tổng số bài nộp: {total_submissions}")
    
    cursor.execute('SELECT AVG(score) FROM submissions')
    avg_score = cursor.fetchone()[0]
    print(f"📊 Điểm trung bình: {avg_score:.1f}")
    
    # Thống kê theo sinh viên
    cursor.execute('''
        SELECT u.username, COUNT(s.id) as submissions, AVG(s.score) as avg_score
        FROM users u
        LEFT JOIN submissions s ON u.id = s.student_id
        WHERE u.role = 'student'
        GROUP BY u.id, u.username
        ORDER BY avg_score DESC
    ''')
    
    print(f"\n👥 THỐNG KÊ THEO SINH VIÊN:")
    print("-" * 40)
    for username, submissions, avg_score in cursor.fetchall():
        avg_score = avg_score or 0
        print(f"   {username}: {submissions} bài nộp, TB: {avg_score:.1f}")
    
    conn.close()

if __name__ == "__main__":
    print("🚀 DEMO AI ASSESSMENT SYSTEM")
    print("=" * 50)
    
    create_demo_data()
    show_demo_stats()
    
    print("\n🎯 HƯỚNG DẪN TEST:")
    print("1. Khởi động server: python main.py")
    print("2. Đăng nhập với student1/password123")
    print("3. Vào 'Đánh giá năng lực' để xem phân tích AI")
    print("4. Vào 'Gợi ý học tập AI' để xem recommendations")
    print("5. Đăng nhập với teacher1/password123")
    print("6. Vào 'Đánh giá năng lực học viên' để xem báo cáo")
    print("\n✨ Hệ thống AI sẽ phân tích và đưa ra gợi ý dựa trên dữ liệu này!")
