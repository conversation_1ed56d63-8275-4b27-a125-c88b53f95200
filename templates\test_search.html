<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Search - CS466</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; background: #f8f9fa; }
        .test-container { background: white; padding: 30px; border-radius: 10px; margin-bottom: 20px; }
        .result-item { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .loading { text-align: center; padding: 20px; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { background: #d1edff; color: #004085; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h1>🧪 Test Search Page</h1>
            <p>Trang test đơn giản để debug search API</p>
            
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-primary mb-3" onclick="testStats()">Test Stats API</button>
                    <div id="stats-result"></div>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-success mb-3" onclick="testSearch()">Test Search API</button>
                    <div id="search-result"></div>
                </div>
            </div>
            
            <hr>
            
            <div class="mb-3">
                <input type="text" id="search-input" class="form-control" placeholder="Nhập từ khóa tìm kiếm...">
                <button class="btn btn-info mt-2" onclick="performSearch()">Tìm kiếm</button>
            </div>
            
            <div id="results-container"></div>
        </div>
        
        <div class="test-container">
            <h3>📋 Console Log</h3>
            <div id="console-log" style="background: #f1f3f4; padding: 15px; border-radius: 5px; font-family: monospace; max-height: 300px; overflow-y: auto;"></div>
        </div>
    </div>

    <script>
        // Custom console.log to display on page
        const originalLog = console.log;
        const originalError = console.error;
        
        function addToConsole(type, ...args) {
            const logDiv = document.getElementById('console-log');
            const time = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
            logDiv.innerHTML += `<div style="color: ${color}; margin: 2px 0;">[${time}] ${type.toUpperCase()}: ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole('log', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole('error', ...args);
        };
        
        // Test functions
        async function testStats() {
            console.log('🔧 Testing /search/stats...');
            const resultDiv = document.getElementById('stats-result');
            
            try {
                const response = await fetch('/search/stats');
                console.log('Stats response status:', response.status);
                
                const data = await response.json();
                console.log('Stats data:', data);
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">✅ Stats API hoạt động!<br>Assignments: ${data.stats.total_searchable_items.assignments}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Stats failed: ${data.error}</div>`;
                }
            } catch (error) {
                console.error('Stats error:', error);
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        async function testSearch() {
            console.log('🔧 Testing /search...');
            const resultDiv = document.getElementById('search-result');
            
            try {
                const formData = new FormData();
                formData.append('query', '');
                formData.append('category', 'assignments');
                formData.append('filters', '{}');
                formData.append('sort_by', 'title');
                formData.append('limit', '3');
                formData.append('offset', '0');
                
                const response = await fetch('/search', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('Search response status:', response.status);
                
                const data = await response.json();
                console.log('Search data:', data);
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">✅ Search API hoạt động!<br>Tìm thấy: ${data.data.total_results} assignments</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Search failed: ${data.error}</div>`;
                }
            } catch (error) {
                console.error('Search error:', error);
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        async function performSearch() {
            console.log('🔍 Performing search...');
            const query = document.getElementById('search-input').value;
            const container = document.getElementById('results-container');
            
            container.innerHTML = '<div class="loading">Đang tìm kiếm...</div>';
            
            try {
                const formData = new FormData();
                formData.append('query', query);
                formData.append('category', 'assignments');
                formData.append('filters', '{}');
                formData.append('sort_by', 'title');
                formData.append('limit', '12');
                formData.append('offset', '0');
                
                console.log('Sending search request for:', query);
                
                const response = await fetch('/search', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                console.log('Search result:', result);
                
                if (result.success && result.data) {
                    displayResults(result.data);
                } else {
                    container.innerHTML = `<div class="error">❌ ${result.error || 'Unknown error'}</div>`;
                }
            } catch (error) {
                console.error('Search error:', error);
                container.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        function displayResults(data) {
            console.log('📋 Displaying results...');
            const container = document.getElementById('results-container');
            
            if (!data.results || data.results.length === 0) {
                container.innerHTML = '<div class="alert alert-info">Không tìm thấy kết quả</div>';
                return;
            }
            
            let html = `<h4>Tìm thấy ${data.total_results} kết quả (${data.search_time}s)</h4>`;
            
            data.results.forEach(result => {
                html += `
                    <div class="result-item">
                        <h5>${result.title}</h5>
                        <p>${result.description || 'Không có mô tả'}</p>
                        <small>Ngôn ngữ: ${result.language} | Giáo viên: ${result.teacher_name}</small>
                    </div>
                `;
            });
            
            container.innerHTML = html;
            console.log('✅ Results displayed successfully');
        }
        
        // Auto-run tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Test page loaded');
            testStats();
            setTimeout(testSearch, 1000);
        });
    </script>
</body>
</html> 