<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>T<PERSON><PERSON> ki<PERSON><PERSON> nâng cao - CS466</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .search-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 2rem 0;
            overflow: hidden;
        }

        .search-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .search-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .search-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .search-main {
            padding: 2rem;
        }

        .search-form {
            background: var(--light-color);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .search-input-group {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .search-input {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 1rem 1rem 1rem 3rem;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            width: 100%;
        }

        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            outline: none;
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            font-size: 1.2rem;
        }

        .category-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .category-tab {
            padding: 0.75rem 1.5rem;
            border: 2px solid #e5e7eb;
            border-radius: 25px;
            background: white;
            color: #6b7280;
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .category-tab:hover, .category-tab.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            text-decoration: none;
        }

        .filters-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
        }

        .filter-group {
            margin-bottom: 1rem;
        }

        .filter-label {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
            display: block;
        }

        .filter-select {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 0.5rem;
            width: 100%;
            transition: border-color 0.3s ease;
        }

        .filter-select:focus {
            border-color: var(--primary-color);
            outline: none;
        }

        .search-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 1.5rem;
        }

        .btn-search {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-search:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
            color: white;
        }

        .btn-clear {
            background: #6b7280;
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-clear:hover {
            background: #4b5563;
            color: white;
        }

        .results-section {
            margin-top: 2rem;
        }

        .results-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .results-info {
            color: #6b7280;
            font-size: 0.95rem;
        }

        .sort-options {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .sort-select {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 0.5rem;
            font-size: 0.9rem;
        }

        .result-item {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .result-item:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transform: translateY(-3px);
            border-color: var(--primary-color);
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        @media (max-width: 768px) {
            .results-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }

        .result-type {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .result-type.assignment {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .result-type.user {
            background: rgba(6, 182, 212, 0.1);
            color: var(--info-color);
        }

        .result-type.course {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .result-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }

        .result-description {
            color: #6b7280;
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .result-meta {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            font-size: 0.9rem;
            color: #6b7280;
        }

        .result-meta span {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .pagination {
            justify-content: center;
            margin-top: 2rem;
        }

        .page-link {
            border-radius: 8px;
            margin: 0 0.25rem;
            border: 1px solid #e5e7eb;
            color: var(--primary-color);
        }

        .page-link:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .page-item.active .page-link {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .suggestions {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .suggestion-item {
            display: inline-block;
            background: white;
            border: 1px solid #0ea5e9;
            border-radius: 15px;
            padding: 0.25rem 0.75rem;
            margin: 0.25rem;
            color: #0ea5e9;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .suggestion-item:hover {
            background: #0ea5e9;
            color: white;
            text-decoration: none;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: #6b7280;
        }

        .no-results {
            text-align: center;
            padding: 3rem;
            color: #6b7280;
        }

        .no-results i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .search-header h1 {
                font-size: 2rem;
            }
            
            .search-main {
                padding: 1rem;
            }
            
            .search-form {
                padding: 1.5rem;
            }
            
            .search-actions {
                flex-direction: column;
            }
            
            .results-header {
                flex-direction: column;
                align-items: flex-start;
            }
        }

        mark {
            background-color: yellow;
            padding: 0.1rem 0.2rem;
            border-radius: 3px;
        }
        
        /* Enhanced no results styling */
        .search-suggestions {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 1rem;
            text-align: left;
        }
        
        .search-suggestions h6 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .search-suggestions ul {
            list-style: none;
            padding-left: 0;
            margin-bottom: 1.5rem;
        }
        
        .search-suggestions li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
            color: #6c757d;
        }
        
        .search-suggestions li:last-child {
            border-bottom: none;
        }
        
        .search-suggestions li:before {
            content: "💡";
            margin-right: 0.5rem;
        }
        
        .suggestion-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        
        .suggestion-tag {
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
        }
        
        .suggestion-tag:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }
        
        /* Scroll to top button */
        .scroll-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }
        
        .scroll-to-top:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
        }
        
        .scroll-to-top.show {
            opacity: 1;
            visibility: visible;
        }
        
        .scroll-to-top:active {
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="search-container">
                    <div class="search-header" style="position: relative;">
                        <!-- Back to Dashboard Button - Left side -->
                        <div style="position: absolute; left: 2rem; top: 2rem; z-index: 10;">
                            <button class="btn btn-outline-light btn-sm" onclick="goBackToDashboard()" 
                                    style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); backdrop-filter: blur(10px);">
                                <i class="fas fa-arrow-left me-1"></i> Quay lại Dashboard
                            </button>
                        </div>
                        
                        <h1><i class="fas fa-search"></i> Tìm kiếm nâng cao</h1>
                        <p>Tìm kiếm bài tập, người dùng và khóa học một cách nhanh chóng và chính xác</p>
                        <div id="total-stats" class="mt-2" style="opacity: 0.8; font-size: 0.9rem;">
                            <i class="fas fa-database"></i> Đang tải thống kê...
                        </div>
                    </div>

                    <div class="search-main">
                        <!-- Search Form -->
                        <form id="search-form" class="search-form">
                            <!-- Search Input -->
                            <div class="search-input-group">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="search-query" class="search-input" 
                                       placeholder="Nhập từ khóa tìm kiếm..." autocomplete="off">
                            </div>

                            <!-- Category Tabs -->
                            <div class="category-tabs">
                                <a href="#" class="category-tab" data-category="all">
                                    <i class="fas fa-globe"></i> Tất cả
                                </a>
                                <a href="#" class="category-tab active" data-category="assignments">
                                    <i class="fas fa-tasks"></i> Bài tập
                                </a>
                                <a href="#" class="category-tab" data-category="users">
                                    <i class="fas fa-users"></i> Người dùng
                                </a>
                                <a href="#" class="category-tab" data-category="courses">
                                    <i class="fas fa-book"></i> Khóa học
                                </a>
                            </div>

                            <!-- Filters -->
                            <div class="filters-section">
                                <h6><i class="fas fa-filter"></i> Bộ lọc nâng cao</h6>
                                <div class="row" id="filters-container">
                                    <!-- Filters will be loaded dynamically -->
                                </div>
                            </div>

                            <!-- Search Actions -->
                            <div class="search-actions">
                                <button type="submit" class="btn-search">
                                    <i class="fas fa-search"></i> Tìm kiếm
                                </button>
                                <button type="button" class="btn-clear" onclick="clearSearch()">
                                    <i class="fas fa-times"></i> Xóa
                                </button>
                            </div>
                        </form>

                        <!-- Suggestions -->
                        <div id="suggestions" class="suggestions" style="display: none;">
                            <h6><i class="fas fa-lightbulb"></i> Gợi ý tìm kiếm:</h6>
                            <div id="suggestions-list"></div>
                        </div>

                        <!-- Results Section -->
                        <div id="results-section" class="results-section" style="display: none;">
                            <!-- Results Header -->
                            <div class="results-header">
                                <div class="results-info" id="results-info">
                                    Tìm thấy 0 kết quả
                                </div>
                                <div class="sort-options">
                                    <label for="sort-select" class="me-2">Sắp xếp:</label>
                                    <select id="sort-select" class="sort-select" onchange="sortResults()">
                                        <option value="relevance">Độ liên quan</option>
                                        <option value="date_desc">Mới nhất</option>
                                        <option value="date_asc">Cũ nhất</option>
                                        <option value="title">Tên A-Z</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Results List -->
                            <div id="results-list" class="results-grid">
                                <!-- Results will be loaded here -->
                            </div>

                            <!-- Pagination -->
                            <nav aria-label="Search results pagination">
                                <ul class="pagination" id="pagination">
                                    <!-- Pagination will be loaded here -->
                                </ul>
                            </nav>
                        </div>

                        <!-- Loading -->
                        <div id="loading" class="loading" style="display: none;">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                            <p>Đang tìm kiếm...</p>
                        </div>

                        <!-- No Results -->
                        <div id="no-results" class="no-results" style="display: none;">
                            <i class="fas fa-search"></i>
                            <h4>Không tìm thấy kết quả</h4>
                            <p id="no-results-message">Hãy thử với từ khóa khác hoặc điều chỉnh bộ lọc</p>
                            <div class="mt-3">
                                <button class="btn btn-outline-primary" onclick="clearSearch()">
                                    <i class="fas fa-refresh"></i> Xem tất cả bài tập
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Scroll to Top Button -->
    <button class="scroll-to-top" id="scrollToTop" onclick="scrollToTop()" title="Lên đầu trang">
        <i class="fas fa-chevron-up"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentCategory = 'assignments';
        let currentPage = 1;
        let currentQuery = '';
        let currentFilters = {};
        let currentSort = 'title';
        let searchResults = [];

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Set default to assignments category and title sort for A-Z display
            currentCategory = 'assignments';
            currentSort = 'title';
            
            // Update active tab to assignments
            document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
            document.querySelector('[data-category="assignments"]').classList.add('active');
            
            // Set default sort option to A-Z
            document.getElementById('sort-select').value = 'title';
            
            loadFilters();
            loadSuggestions();
            loadStats();
            
            // Load all assignments immediately on page load, sorted A-Z
            performSearch(1);
            
            // Auto search on input with debounce
            let searchTimeout;
            document.getElementById('search-query').addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    performSearch(1);
                }, 500);
            });
        });

        // Load statistics
        async function loadStats() {
            try {
                console.log('📊 Loading stats...');
                const response = await fetch('/search/stats');
                console.log('📊 Stats response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                console.log('📊 Stats data:', result);
                
                if (result.success) {
                    const stats = result.stats;
                    const totalItems = Object.values(stats.total_searchable_items).reduce((a, b) => a + b, 0);
                    document.getElementById('total-stats').innerHTML = `
                        <i class="fas fa-database"></i> 
                        Tổng cộng: ${totalItems} mục 
                        (${stats.total_searchable_items.assignments} bài tập, 
                        ${stats.total_searchable_items.users} người dùng, 
                        ${stats.total_searchable_items.courses} khóa học)
                    `;
                } else {
                    console.error('❌ Stats failed:', result.error);
                    document.getElementById('total-stats').innerHTML = `
                        <i class="fas fa-database"></i> Hệ thống tìm kiếm sẵn sàng (lỗi stats)
                    `;
                }
            } catch (error) {
                console.error('❌ Error loading stats:', error);
                console.error('❌ Stats error details:', error.message, error.stack);
                document.getElementById('total-stats').innerHTML = `
                    <i class="fas fa-database"></i> Hệ thống tìm kiếm sẵn sàng (lỗi kết nối)
                `;
            }
        }

        // Category tab switching
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Update active tab
                document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                // Update current category
                currentCategory = this.dataset.category;
                
                // Reload filters
                loadFilters();
                
                // Always perform search to show results
                performSearch(1);
            });
        });

        // Search form submission
        document.getElementById('search-form').addEventListener('submit', function(e) {
            e.preventDefault();
            performSearch();
        });

        // Load filters for current category
        async function loadFilters() {
            try {
                const response = await fetch(`/search/filters/${currentCategory}`);
                const result = await response.json();
                
                if (result.success) {
                    displayFilters(result.filters);
                }
            } catch (error) {
                console.error('Error loading filters:', error);
            }
        }

        // Display filters
        function displayFilters(filters) {
            const container = document.getElementById('filters-container');
            container.innerHTML = '';
            
            Object.entries(filters).forEach(([filterKey, filterValues]) => {
                const col = document.createElement('div');
                col.className = 'col-md-3 filter-group';
                
                const label = document.createElement('label');
                label.className = 'filter-label';
                label.textContent = getFilterLabel(filterKey);
                
                const select = document.createElement('select');
                select.className = 'filter-select';
                select.id = `filter-${filterKey}`;
                select.addEventListener('change', updateFilters);
                
                // Add default option
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = 'Tất cả';
                select.appendChild(defaultOption);
                
                // Add filter options
                filterValues.forEach(value => {
                    const option = document.createElement('option');
                    option.value = value;
                    option.textContent = getFilterValueLabel(filterKey, value);
                    select.appendChild(option);
                });
                
                col.appendChild(label);
                col.appendChild(select);
                container.appendChild(col);
            });
        }

        // Get filter label
        function getFilterLabel(filterKey) {
            const labels = {
                'difficulty': 'Độ khó',
                'language': 'Ngôn ngữ',
                'assignment_type': 'Loại bài tập',
                'date_range': 'Thời gian',
                'status': 'Trạng thái',
                'role': 'Vai trò',
                'active_status': 'Trạng thái hoạt động',
                'topic': 'Chủ đề'
            };
            return labels[filterKey] || filterKey;
        }

        // Get filter value label
        function getFilterValueLabel(filterKey, value) {
            const labels = {
                'difficulty': {
                    'easy': 'Dễ',
                    'medium': 'Trung bình',
                    'hard': 'Khó'
                },
                'language': {
                    'python': 'Python',
                    'perl': 'Perl'
                },
                'assignment_type': {
                    'code': 'Lập trình',
                    'quiz': 'Trắc nghiệm',
                    'file': 'Upload file'
                },
                'date_range': {
                    'today': 'Hôm nay',
                    'week': 'Tuần này',
                    'month': 'Tháng này',
                    'year': 'Năm này'
                },
                'status': {
                    'active': 'Đang hoạt động',
                    'completed': 'Đã hoàn thành',
                    'overdue': 'Quá hạn'
                },
                'role': {
                    'student': 'Sinh viên',
                    'teacher': 'Giáo viên',
                    'admin': 'Quản trị'
                },
                'active_status': {
                    'active': 'Hoạt động',
                    'inactive': 'Không hoạt động'
                }
            };
            
            return labels[filterKey]?.[value] || value;
        }

        // Update filters
        function updateFilters() {
            currentFilters = {};
            
            document.querySelectorAll('.filter-select').forEach(select => {
                if (select.value) {
                    const filterKey = select.id.replace('filter-', '');
                    currentFilters[filterKey] = select.value;
                }
            });
            
            // Always perform search to refresh results
            performSearch(1);
        }

        // Load suggestions
        async function loadSuggestions() {
            try {
                const response = await fetch(`/search/suggestions?category=${currentCategory}`);
                const result = await response.json();
                
                if (result.success && result.suggestions.length > 0) {
                    displaySuggestions(result.suggestions);
                }
            } catch (error) {
                console.error('Error loading suggestions:', error);
            }
        }

        // Display suggestions
        function displaySuggestions(suggestions) {
            const container = document.getElementById('suggestions-list');
            container.innerHTML = '';
            
            suggestions.forEach(suggestion => {
                const link = document.createElement('a');
                link.className = 'suggestion-item';
                link.href = '#';
                link.textContent = suggestion;
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.getElementById('search-query').value = suggestion;
                    performSearch();
                });
                container.appendChild(link);
            });
            
            document.getElementById('suggestions').style.display = 'block';
        }

        // Perform search
        async function performSearch(page = 1) {
            console.log('🔍 Starting search...', {currentCategory, currentSort, page});
            
            currentQuery = document.getElementById('search-query').value.trim();
            currentPage = page;
            
            // Allow empty query for browsing all items
            showLoading();
            
            try {
                const formData = new FormData();
                formData.append('query', currentQuery || '');
                formData.append('category', currentCategory);
                formData.append('filters', JSON.stringify(currentFilters));
                formData.append('sort_by', currentSort);
                formData.append('limit', '12');
                formData.append('offset', (page - 1) * 12);
                
                console.log('📤 Sending request:', {
                    query: currentQuery || '',
                    category: currentCategory,
                    filters: currentFilters,
                    sort_by: currentSort,
                    limit: 12,
                    offset: (page - 1) * 12
                });
                
                const response = await fetch('/search', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('📥 Response status:', response.status);
                console.log('📥 Response headers:', response.headers);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                console.log('📊 Response data:', result);
                
                hideLoading();
                
                if (result.success) {
                    searchResults = result.data;
                    displayResults(result.data);
                } else {
                    console.error('❌ Search failed:', result.error);
                    showError(result.error || 'Lỗi tìm kiếm');
                }
            } catch (error) {
                hideLoading();
                console.error('💥 Search error:', error);
                console.error('💥 Error details:', error.message, error.stack);
                showError('Lỗi kết nối: ' + error.message);
            }
        }

        // Display results
        function displayResults(data) {
            console.log('📋 displayResults called with data:', data);
            
            const resultsSection = document.getElementById('results-section');
            const resultsList = document.getElementById('results-list');
            const resultsInfo = document.getElementById('results-info');
            
            // Validate data structure
            if (!data) {
                console.error('❌ displayResults: data is null/undefined');
                showError('Dữ liệu phản hồi không hợp lệ');
                return;
            }
            
            if (!data.results) {
                console.error('❌ displayResults: data.results is undefined', data);
                showError('Cấu trúc dữ liệu không đúng');
                return;
            }
            
            if (!Array.isArray(data.results)) {
                console.error('❌ displayResults: data.results is not an array', typeof data.results, data.results);
                showError('Dữ liệu kết quả không phải là mảng');
                return;
            }
            
            console.log('✅ displayResults: data validation passed, results count:', data.results.length);
            
            if (data.results.length === 0) {
                console.log('📋 No results found, showing no results message');
                showNoResults();
                return;
            }
            
            // Update results info
            const searchText = currentQuery ? ` cho "${currentQuery}"` : '';
            const totalResults = data.total_results || data.results.length;
            const searchTime = data.search_time || 0;
            resultsInfo.textContent = `Tìm thấy ${totalResults} kết quả${searchText} trong ${searchTime}s`;
            
            // Display results
            resultsList.innerHTML = '';
            console.log('📋 Creating result items...');
            data.results.forEach((result, index) => {
                try {
                    console.log(`📋 Creating item ${index + 1}:`, result.title);
                    const resultItem = createResultItem(result);
                    resultsList.appendChild(resultItem);
                } catch (error) {
                    console.error(`❌ Error creating result item ${index + 1}:`, error, result);
                }
            });
            
            // Show pagination
            console.log('📋 Setting up pagination for', totalResults, 'results');
            displayPagination(totalResults);
            
            resultsSection.style.display = 'block';
            document.getElementById('no-results').style.display = 'none';
            
            console.log('✅ displayResults completed successfully');
        }

        // Create result item
        function createResultItem(result) {
            const item = document.createElement('div');
            item.className = 'result-item';
            
            // Add click handler for assignments
            if (result.type === 'assignment') {
                item.onclick = () => {
                    window.open(`/assignment/${result.id}`, '_blank');
                };
            }
            
            const typeSpan = document.createElement('span');
            typeSpan.className = `result-type ${result.type}`;
            typeSpan.textContent = getTypeLabel(result.type);
            
            const title = document.createElement('div');
            title.className = 'result-title';
            title.innerHTML = result.highlight || result.title;
            
            const description = document.createElement('div');
            description.className = 'result-description';
            // Truncate long descriptions
            const desc = result.description || '';
            description.textContent = desc.length > 120 ? desc.substring(0, 120) + '...' : desc;
            
            const meta = document.createElement('div');
            meta.className = 'result-meta';
            meta.innerHTML = createMetaInfo(result);
            
            // Add difficulty badge for assignments
            if (result.type === 'assignment' && result.difficulty) {
                const difficultyBadge = document.createElement('div');
                difficultyBadge.className = `badge badge-${result.difficulty}`;
                difficultyBadge.textContent = getFilterValueLabel('difficulty', result.difficulty);
                difficultyBadge.style.cssText = `
                    position: absolute;
                    top: 1rem;
                    right: 1rem;
                    padding: 0.25rem 0.75rem;
                    border-radius: 15px;
                    font-size: 0.8rem;
                    font-weight: 600;
                `;
                
                // Set badge color based on difficulty
                if (result.difficulty === 'easy') {
                    difficultyBadge.style.backgroundColor = 'rgba(16, 185, 129, 0.1)';
                    difficultyBadge.style.color = '#10b981';
                } else if (result.difficulty === 'medium') {
                    difficultyBadge.style.backgroundColor = 'rgba(245, 158, 11, 0.1)';
                    difficultyBadge.style.color = '#f59e0b';
                } else {
                    difficultyBadge.style.backgroundColor = 'rgba(239, 68, 68, 0.1)';
                    difficultyBadge.style.color = '#ef4444';
                }
                
                item.style.position = 'relative';
                item.appendChild(difficultyBadge);
            }
            
            item.appendChild(typeSpan);
            item.appendChild(title);
            if (description.textContent) {
                item.appendChild(description);
            }
            item.appendChild(meta);
            
            return item;
        }

        // Get type label
        function getTypeLabel(type) {
            const labels = {
                'assignment': 'Bài tập',
                'user': 'Người dùng',
                'course': 'Khóa học'
            };
            return labels[type] || type;
        }

        // Create meta info
        function createMetaInfo(result) {
            let meta = '';
            
            if (result.type === 'assignment') {
                meta += `<span><i class="fas fa-code"></i> ${result.language}</span>`;
                meta += `<span><i class="fas fa-user"></i> ${result.teacher_name}</span>`;
                if (result.deadline) {
                    meta += `<span><i class="fas fa-clock"></i> ${formatDate(result.deadline)}</span>`;
                }
            } else if (result.type === 'user') {
                meta += `<span><i class="fas fa-envelope"></i> ${result.email}</span>`;
                meta += `<span><i class="fas fa-user-tag"></i> ${getFilterValueLabel('role', result.role)}</span>`;
            } else if (result.type === 'course') {
                meta += `<span><i class="fas fa-code"></i> ${result.language}</span>`;
                meta += `<span><i class="fas fa-signal"></i> ${getFilterValueLabel('difficulty', result.difficulty)}</span>`;
            }
            
            if (result.created_at) {
                meta += `<span><i class="fas fa-calendar"></i> ${formatDate(result.created_at)}</span>`;
            }
            
            return meta;
        }

        // Format date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('vi-VN');
        }

        // Display pagination
        function displayPagination(totalResults) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';
            
            const itemsPerPage = 12;
            const totalPages = Math.ceil(totalResults / itemsPerPage);
            
            if (totalPages <= 1) return;
            
            // Previous button
            if (currentPage > 1) {
                const prevLi = document.createElement('li');
                prevLi.className = 'page-item';
                prevLi.innerHTML = `<a class="page-link" href="#" onclick="performSearch(${currentPage - 1})">
                    <i class="fas fa-chevron-left"></i> Trước
                </a>`;
                pagination.appendChild(prevLi);
            }
            
            // First page
            if (currentPage > 3) {
                const firstLi = document.createElement('li');
                firstLi.className = 'page-item';
                firstLi.innerHTML = `<a class="page-link" href="#" onclick="performSearch(1)">1</a>`;
                pagination.appendChild(firstLi);
                
                if (currentPage > 4) {
                    const dotsLi = document.createElement('li');
                    dotsLi.className = 'page-item disabled';
                    dotsLi.innerHTML = `<span class="page-link">...</span>`;
                    pagination.appendChild(dotsLi);
                }
            }
            
            // Page numbers around current page
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="performSearch(${i})">${i}</a>`;
                pagination.appendChild(li);
            }
            
            // Last page
            if (currentPage < totalPages - 2) {
                if (currentPage < totalPages - 3) {
                    const dotsLi = document.createElement('li');
                    dotsLi.className = 'page-item disabled';
                    dotsLi.innerHTML = `<span class="page-link">...</span>`;
                    pagination.appendChild(dotsLi);
                }
                
                const lastLi = document.createElement('li');
                lastLi.className = 'page-item';
                lastLi.innerHTML = `<a class="page-link" href="#" onclick="performSearch(${totalPages})">${totalPages}</a>`;
                pagination.appendChild(lastLi);
            }
            
            // Next button
            if (currentPage < totalPages) {
                const nextLi = document.createElement('li');
                nextLi.className = 'page-item';
                nextLi.innerHTML = `<a class="page-link" href="#" onclick="performSearch(${currentPage + 1})">
                    Sau <i class="fas fa-chevron-right"></i>
                </a>`;
                pagination.appendChild(nextLi);
            }
            
            // Add page info
            const pageInfo = document.createElement('li');
            pageInfo.className = 'page-item disabled ms-3';
            pageInfo.innerHTML = `<span class="page-link">Trang ${currentPage} / ${totalPages}</span>`;
            pagination.appendChild(pageInfo);
        }

        // Sort results
        function sortResults() {
            currentSort = document.getElementById('sort-select').value;
            performSearch(1);
        }

        // Clear search
        function clearSearch() {
            document.getElementById('search-query').value = '';
            document.querySelectorAll('.filter-select').forEach(select => {
                select.value = '';
            });
            currentQuery = '';
            currentFilters = {};
            // Perform search to show all assignments
            performSearch(1);
        }

        // Show loading
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results-section').style.display = 'none';
            document.getElementById('no-results').style.display = 'none';
        }

        // Hide loading
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        // Show no results
        function showNoResults() {
            const noResultsDiv = document.getElementById('no-results');
            const resultsInfo = document.getElementById('results-info');
            
            // Update results info
            if (currentQuery) {
                resultsInfo.textContent = `Không tìm thấy kết quả cho "${currentQuery}"`;
            } else {
                resultsInfo.textContent = 'Không có bài tập nào trong danh sách';
            }
            
            // Create enhanced no results message
            let noResultsHTML = `
                <div class="no-results">
                    <i class="fas fa-search-minus"></i>
                    <h4>Không tìm thấy kết quả</h4>
            `;
            
            if (currentQuery) {
                noResultsHTML += `
                    <p>Không có kết quả nào phù hợp với từ khóa <strong>"${currentQuery}"</strong></p>
                    <div class="search-suggestions">
                        <h6><i class="fas fa-lightbulb"></i> Gợi ý tìm kiếm:</h6>
                        <ul>
                            <li>Kiểm tra chính tả từ khóa</li>
                            <li>Thử sử dụng từ khóa khác hoặc ngắn gọn hơn</li>
                            <li>Sử dụng từ khóa tiếng Anh: "python", "loop", "function"</li>
                            <li>Thử tìm kiếm với ký tự đơn: "a", "b", "c"</li>
                        </ul>
                        <div class="quick-searches">
                            <p><strong>Tìm kiếm phổ biến:</strong></p>
                            <div class="suggestion-tags">
                                <span class="suggestion-tag" onclick="quickSearch('python')">Python</span>
                                <span class="suggestion-tag" onclick="quickSearch('array')">Array</span>
                                <span class="suggestion-tag" onclick="quickSearch('loop')">Loop</span>
                                <span class="suggestion-tag" onclick="quickSearch('function')">Function</span>
                                <span class="suggestion-tag" onclick="quickSearch('calculator')">Calculator</span>
                                <span class="suggestion-tag" onclick="quickSearch('error')">Error</span>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                noResultsHTML += `
                    <p>Hiện tại không có bài tập nào trong hệ thống</p>
                    <p>Vui lòng thử lại sau hoặc liên hệ quản trị viên</p>
                `;
            }
            
            noResultsHTML += `
                    <button class="btn btn-primary mt-3" onclick="clearSearch()">
                        <i class="fas fa-refresh"></i> Xem tất cả bài tập
                    </button>
                </div>
            `;
            
            noResultsDiv.innerHTML = noResultsHTML;
            noResultsDiv.style.display = 'block';
            document.getElementById('results-section').style.display = 'none';
        }
        
        // Quick search function
        function quickSearch(keyword) {
            document.getElementById('search-query').value = keyword;
            currentQuery = keyword;
            performSearch(1);
        }

        // Hide results
        function hideResults() {
            document.getElementById('results-section').style.display = 'none';
            document.getElementById('no-results').style.display = 'none';
        }

        // Show error
        function showError(message) {
            console.error('❌ Showing error:', message);
            document.getElementById('no-results-message').textContent = message;
            document.getElementById('no-results').style.display = 'block';
            document.getElementById('results-section').style.display = 'none';
        }
        
        // Go back to dashboard
        function goBackToDashboard() {
            // Try to detect user role from localStorage or default to student
            const userRole = localStorage.getItem('user_role') || 'student';
            
            // If opened in new tab, close it and focus parent
            if (window.opener) {
                window.close();
                window.opener.focus();
            } else {
                // Otherwise navigate to dashboard
                window.location.href = `/dashboard/${userRole}`;
            }
        }
        
        // Scroll to top functionality
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
        
        // Show/hide scroll to top button based on scroll position
        window.addEventListener('scroll', function() {
            const scrollToTopBtn = document.getElementById('scrollToTop');
            if (window.pageYOffset > 300) {
                scrollToTopBtn.classList.add('show');
            } else {
                scrollToTopBtn.classList.remove('show');
            }
        });
    </script>
</body>
</html> 