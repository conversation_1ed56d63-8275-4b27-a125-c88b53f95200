"""
AI Code Assistant - Hỗ trợ gợi ý code thông minh
"""

import json
import re
from typing import List, Dict, Optional
from datetime import datetime

class AICodeAssistant:
    def __init__(self):
        self.code_patterns = {
            "python": {
                "function": r"def\s+(\w+)\s*\(",
                "class": r"class\s+(\w+)\s*:",
                "import": r"import\s+(\w+)",
                "variable": r"(\w+)\s*=",
                "loop": r"for\s+\w+\s+in",
                "condition": r"if\s+.+:"
            },
            "perl": {
                "subroutine": r"sub\s+(\w+)\s*{",
                "variable": r"my\s+\$(\w+)",
                "array": r"my\s+@(\w+)",
                "hash": r"my\s+%(\w+)",
                "loop": r"foreach\s+my\s+\$\w+",
                "condition": r"if\s*\("
            }
        }
        
        self.code_templates = {
            "python": {
                "basic_function": {
                    "title": "Basic Function",
                    "description": "Create a simple function with parameters and return value",
                    "code": """def function_name(parameter1, parameter2):
    \"\"\"
    Function description here
    \"\"\"
    # Your code here
    result = parameter1 + parameter2
    return result"""
                },
                "class_template": {
                    "title": "Class Template",
                    "description": "Basic class structure with constructor and methods",
                    "code": """class ClassName:
    def __init__(self, parameter):
        self.attribute = parameter
    
    def method_name(self):
        \"\"\"Method description\"\"\"
        return self.attribute"""
                },
                "file_handling": {
                    "title": "File Handling",
                    "description": "Read and write files safely",
                    "code": """# Reading a file
try:
    with open('filename.txt', 'r') as file:
        content = file.read()
        print(content)
except FileNotFoundError:
    print("File not found!")

# Writing to a file
with open('output.txt', 'w') as file:
    file.write("Hello, World!")"""
                },
                "error_handling": {
                    "title": "Error Handling",
                    "description": "Try-except block for error handling",
                    "code": """try:
    # Code that might raise an exception
    result = 10 / 0
except ZeroDivisionError:
    print("Cannot divide by zero!")
except Exception as e:
    print(f"An error occurred: {e}")
finally:
    print("This always executes")"""
                },
                "list_comprehension": {
                    "title": "List Comprehension",
                    "description": "Create lists using comprehension",
                    "code": """# Basic list comprehension
numbers = [1, 2, 3, 4, 5]
squares = [x**2 for x in numbers]

# With condition
even_squares = [x**2 for x in numbers if x % 2 == 0]

# Nested comprehension
matrix = [[i*j for j in range(3)] for i in range(3)]"""
                }
            },
            "perl": {
                "basic_subroutine": {
                    "title": "Basic Subroutine",
                    "description": "Create a simple subroutine with parameters",
                    "code": """sub subroutine_name {
    my ($param1, $param2) = @_;
    
    # Your code here
    my $result = $param1 + $param2;
    return $result;
}

# Call the subroutine
my $answer = subroutine_name(5, 10);
print "Result: $answer\\n";"""
                },
                "array_operations": {
                    "title": "Array Operations",
                    "description": "Working with arrays in Perl",
                    "code": """# Create and manipulate arrays
my @fruits = ("apple", "banana", "orange");

# Add elements
push @fruits, "grape";

# Loop through array
foreach my $fruit (@fruits) {
    print "Fruit: $fruit\\n";
}

# Array size
my $size = @fruits;
print "Array has $size elements\\n";"""
                },
                "hash_operations": {
                    "title": "Hash Operations",
                    "description": "Working with hashes (associative arrays)",
                    "code": """# Create hash
my %person = (
    "name" => "John",
    "age" => 30,
    "city" => "New York"
);

# Access values
print "Name: $person{'name'}\\n";

# Loop through hash
foreach my $key (keys %person) {
    print "$key: $person{$key}\\n";
}"""
                },
                "file_handling": {
                    "title": "File Handling",
                    "description": "Read and write files in Perl",
                    "code": """# Reading a file
open(my $fh, '<', 'filename.txt') or die "Cannot open file: $!";
while (my $line = <$fh>) {
    chomp $line;
    print "Line: $line\\n";
}
close($fh);

# Writing to a file
open(my $out, '>', 'output.txt') or die "Cannot create file: $!";
print $out "Hello, World!\\n";
close($out);"""
                }
            }
        }

    def analyze_code(self, code: str, language: str) -> Dict:
        """Analyze code and provide insights"""
        analysis = {
            "language": language,
            "lines": len(code.split('\n')),
            "characters": len(code),
            "functions": [],
            "variables": [],
            "suggestions": []
        }
        
        if language in self.code_patterns:
            patterns = self.code_patterns[language]
            
            # Find functions/subroutines
            if language == "python":
                functions = re.findall(patterns["function"], code)
                analysis["functions"] = functions
            elif language == "perl":
                subroutines = re.findall(patterns["subroutine"], code)
                analysis["functions"] = subroutines
            
            # Find variables
            variables = re.findall(patterns["variable"], code)
            analysis["variables"] = variables
            
            # Generate suggestions based on code content
            analysis["suggestions"] = self._generate_contextual_suggestions(code, language)
        
        return analysis

    def _generate_contextual_suggestions(self, code: str, language: str) -> List[Dict]:
        """Generate suggestions based on current code context"""
        suggestions = []
        templates = self.code_templates.get(language, {})
        
        # Check what's missing or could be improved
        if language == "python":
            if "def " not in code and len(code.split('\n')) > 5:
                suggestions.append({
                    "type": "improvement",
                    "title": "Consider using functions",
                    "description": "Your code might benefit from being organized into functions",
                    "template": "basic_function"
                })
            
            if "try:" not in code and ("input(" in code or "open(" in code):
                suggestions.append({
                    "type": "improvement", 
                    "title": "Add error handling",
                    "description": "Consider adding try-except blocks for safer code",
                    "template": "error_handling"
                })
            
            if "for " in code and "[" in code and "]" in code:
                suggestions.append({
                    "type": "optimization",
                    "title": "Use list comprehension",
                    "description": "You might be able to simplify your loops with list comprehension",
                    "template": "list_comprehension"
                })
        
        elif language == "perl":
            if "sub " not in code and len(code.split('\n')) > 5:
                suggestions.append({
                    "type": "improvement",
                    "title": "Consider using subroutines",
                    "description": "Your code might benefit from being organized into subroutines",
                    "template": "basic_subroutine"
                })
            
            if "@" not in code and "array" in code.lower():
                suggestions.append({
                    "type": "suggestion",
                    "title": "Array operations",
                    "description": "Learn about Perl array operations",
                    "template": "array_operations"
                })
        
        return suggestions

    def get_suggestion_by_prompt(self, prompt: str, code: str, language: str) -> List[Dict]:
        """Get suggestions based on user prompt"""
        prompt_lower = prompt.lower()
        suggestions = []
        templates = self.code_templates.get(language, {})
        
        # Keyword-based suggestions
        keywords_map = {
            "function": ["basic_function", "class_template"],
            "class": ["class_template"],
            "file": ["file_handling"],
            "error": ["error_handling"],
            "exception": ["error_handling"],
            "list": ["list_comprehension"],
            "array": ["array_operations"],
            "hash": ["hash_operations"],
            "subroutine": ["basic_subroutine"],
            "loop": ["array_operations", "list_comprehension"]
        }
        
        for keyword, template_keys in keywords_map.items():
            if keyword in prompt_lower:
                for template_key in template_keys:
                    if template_key in templates:
                        template = templates[template_key]
                        suggestions.append({
                            "title": template["title"],
                            "description": template["description"],
                            "code": template["code"],
                            "type": "template"
                        })
        
        # If no specific suggestions, provide general help
        if not suggestions:
            if language == "python":
                suggestions = [
                    {
                        "title": "Python Function",
                        "description": "Create a basic Python function",
                        "code": templates["basic_function"]["code"],
                        "type": "template"
                    },
                    {
                        "title": "Error Handling",
                        "description": "Add try-except for safer code",
                        "code": templates["error_handling"]["code"],
                        "type": "template"
                    }
                ]
            elif language == "perl":
                suggestions = [
                    {
                        "title": "Perl Subroutine",
                        "description": "Create a basic Perl subroutine",
                        "code": templates["basic_subroutine"]["code"],
                        "type": "template"
                    },
                    {
                        "title": "Array Operations",
                        "description": "Work with Perl arrays",
                        "code": templates["array_operations"]["code"],
                        "type": "template"
                    }
                ]
        
        return suggestions[:3]  # Return max 3 suggestions

    def get_code_explanation(self, code: str, language: str) -> str:
        """Provide explanation of the code"""
        if not code.strip():
            return "No code to explain."
        
        lines = code.split('\n')
        explanations = []
        
        if language == "python":
            for line in lines[:10]:  # Explain first 10 lines
                line = line.strip()
                if line.startswith('def '):
                    explanations.append(f"• Defines a function: {line}")
                elif line.startswith('class '):
                    explanations.append(f"• Defines a class: {line}")
                elif line.startswith('import ') or line.startswith('from '):
                    explanations.append(f"• Imports a module: {line}")
                elif '=' in line and not line.startswith('#'):
                    explanations.append(f"• Assigns a value: {line}")
                elif line.startswith('print('):
                    explanations.append(f"• Prints output: {line}")
        
        elif language == "perl":
            for line in lines[:10]:
                line = line.strip()
                if line.startswith('sub '):
                    explanations.append(f"• Defines a subroutine: {line}")
                elif line.startswith('my '):
                    explanations.append(f"• Declares a variable: {line}")
                elif line.startswith('print '):
                    explanations.append(f"• Prints output: {line}")
        
        if explanations:
            return "Code explanation:\n" + "\n".join(explanations)
        else:
            return "This code contains basic programming constructs."
