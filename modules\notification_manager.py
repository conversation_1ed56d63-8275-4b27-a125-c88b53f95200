"""
Notification Manager - <PERSON><PERSON><PERSON><PERSON> <PERSON>ý thông báo hệ thống
Tự động tạo thông báo cho bài tập mới, deadline, và sự kiện quan trọng
"""

import sqlite3
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging

# Import email modules only when needed
try:
    import smtplib
    from email.mime.text import MimeText
    from email.mime.multipart import MimeMultipart
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False

class NotificationManager:
    def __init__(self):
        self.db_name = "cs466_database.db"
        
        # Email configuration (cần cấu hình SMTP server)
        self.smtp_server = "smtp.gmail.com"
        self.smtp_port = 587
        self.email_user = ""  # Cần cấu hình
        self.email_password = ""  # Cần cấu hình
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def create_assignment_notification(self, assignment_id: int, teacher_id: int) -> int:
        """T<PERSON><PERSON> thông báo khi có bài tập mới"""
        from database import db
        
        # Lấy thông tin bài tập
        assignment = db.get_assignment_by_id(assignment_id)
        if not assignment:
            return 0
        
        # Tạo thông báo cho tất cả học sinh
        title = f"📚 Bài tập mới: {assignment['title']}"
        message = f"""
        Giáo viên {assignment['teacher_name']} vừa giao bài tập mới:
        
        📝 Tên bài tập: {assignment['title']}
        💻 Ngôn ngữ: {assignment['language']}
        ⏰ Deadline: {self._format_datetime(assignment['deadline'])}
        📊 Độ khó: {self._get_difficulty_text(assignment['difficulty'])}
        
        Hãy vào phần "Bài tập" để xem chi tiết và bắt đầu làm bài!
        """
        
        notification_id = db.create_notification(
            title=title,
            message=message.strip(),
            notification_type="assignment",
            target_role="student",
            created_by=teacher_id
        )
        
        self.logger.info(f"Created assignment notification {notification_id} for assignment {assignment_id}")
        return notification_id
    
    def create_deadline_reminder(self, assignment_id: int, hours_before: int = 24) -> int:
        """Tạo thông báo nhắc nhở deadline"""
        from database import db
        
        assignment = db.get_assignment_by_id(assignment_id)
        if not assignment:
            return 0
        
        title = f"⏰ Nhắc nhở deadline: {assignment['title']}"
        message = f"""
        Bài tập "{assignment['title']}" sắp hết hạn nộp!
        
        ⏰ Deadline: {self._format_datetime(assignment['deadline'])}
        ⏳ Còn lại: {hours_before} giờ
        💻 Ngôn ngữ: {assignment['language']}
        
        Hãy nhanh chóng hoàn thành và nộp bài để không bị trễ deadline!
        """
        
        notification_id = db.create_notification(
            title=title,
            message=message.strip(),
            notification_type="deadline",
            target_role="student",
            created_by=assignment['teacher_id'],
            expires_at=assignment['deadline']
        )
        
        self.logger.info(f"Created deadline reminder {notification_id} for assignment {assignment_id}")
        return notification_id
    
    def create_system_announcement(self, title: str, message: str, 
                                 target_role: str = None, expires_at: str = None) -> int:
        """Tạo thông báo hệ thống"""
        from database import db
        
        notification_id = db.create_notification(
            title=f"📢 {title}",
            message=message,
            notification_type="announcement",
            target_role=target_role,
            expires_at=expires_at
        )
        
        self.logger.info(f"Created system announcement {notification_id}")
        return notification_id
    
    def check_upcoming_deadlines(self, hours_ahead: int = 24) -> List[int]:
        """Kiểm tra và tạo thông báo cho các deadline sắp tới"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        # Tìm các bài tập có deadline trong khoảng thời gian sắp tới
        future_time = datetime.now() + timedelta(hours=hours_ahead)
        
        cursor.execute('''
            SELECT id, title, deadline, teacher_id
            FROM assignments
            WHERE deadline BETWEEN datetime('now') AND ?
            AND id NOT IN (
                SELECT DISTINCT CAST(SUBSTR(title, INSTR(title, 'assignment ') + 11) AS INTEGER)
                FROM notifications 
                WHERE type = 'deadline' 
                AND title LIKE '%Nhắc nhở deadline%'
                AND created_at > datetime('now', '-1 day')
            )
        ''', (future_time.isoformat(),))
        
        assignments = cursor.fetchall()
        conn.close()
        
        notification_ids = []
        for assignment in assignments:
            assignment_id, title, deadline, teacher_id = assignment
            notification_id = self.create_deadline_reminder(assignment_id, hours_ahead)
            if notification_id:
                notification_ids.append(notification_id)
        
        return notification_ids
    
    def send_email_notification(self, user_email: str, subject: str, message: str) -> bool:
        """Gửi thông báo qua email"""
        if not EMAIL_AVAILABLE:
            self.logger.warning("Email modules not available")
            return False

        if not self.email_user or not self.email_password:
            self.logger.warning("Email configuration not set up")
            return False
        
        try:
            # Tạo email
            msg = MimeMultipart()
            msg['From'] = self.email_user
            msg['To'] = user_email
            msg['Subject'] = subject
            
            # Thêm nội dung
            msg.attach(MimeText(message, 'plain', 'utf-8'))
            
            # Gửi email
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.email_user, self.email_password)
            text = msg.as_string()
            server.sendmail(self.email_user, user_email, text)
            server.quit()
            
            self.logger.info(f"Email sent successfully to {user_email}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send email to {user_email}: {str(e)}")
            return False
    
    def send_assignment_email_to_students(self, assignment_id: int) -> int:
        """Gửi email thông báo bài tập mới cho tất cả học sinh"""
        from database import db
        
        # Lấy thông tin bài tập
        assignment = db.get_assignment_by_id(assignment_id)
        if not assignment:
            return 0
        
        # Lấy danh sách email học sinh
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        cursor.execute("SELECT email FROM users WHERE role = 'student' AND email IS NOT NULL")
        student_emails = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        if not student_emails:
            return 0
        
        # Tạo nội dung email
        subject = f"[CS466] Bài tập mới: {assignment['title']}"
        message = f"""
Chào bạn,

Giáo viên {assignment['teacher_name']} vừa giao bài tập mới:

📝 Tên bài tập: {assignment['title']}
📄 Mô tả: {assignment['description']}
💻 Ngôn ngữ: {assignment['language']}
📊 Độ khó: {self._get_difficulty_text(assignment['difficulty'])}
⏰ Deadline: {self._format_datetime(assignment['deadline'])}

Hãy đăng nhập vào hệ thống để xem chi tiết và bắt đầu làm bài:
http://localhost:8000/

Chúc bạn học tập tốt!

---
Hệ thống học tập CS466
        """.strip()
        
        # Gửi email cho từng học sinh
        sent_count = 0
        for email in student_emails:
            if self.send_email_notification(email, subject, message):
                sent_count += 1
        
        self.logger.info(f"Sent assignment email to {sent_count}/{len(student_emails)} students")
        return sent_count
    
    def _format_datetime(self, datetime_str: str) -> str:
        """Format datetime string for display"""
        try:
            dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
            return dt.strftime("%d/%m/%Y %H:%M")
        except:
            return datetime_str
    
    def _get_difficulty_text(self, difficulty: str) -> str:
        """Convert difficulty to Vietnamese text"""
        difficulty_map = {
            'easy': '🟢 Dễ',
            'medium': '🟡 Trung bình', 
            'hard': '🔴 Khó'
        }
        return difficulty_map.get(difficulty, difficulty)
    
    def cleanup_expired_notifications(self) -> int:
        """Xóa các thông báo đã hết hạn"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        cursor.execute('''
            DELETE FROM notifications 
            WHERE expires_at IS NOT NULL 
            AND expires_at < datetime('now')
        ''')
        
        deleted_count = cursor.rowcount
        conn.commit()
        conn.close()
        
        self.logger.info(f"Cleaned up {deleted_count} expired notifications")
        return deleted_count
