"""
AI Recommendation System - <PERSON><PERSON><PERSON> giá năng lực và gợi ý bài học tiếp theo
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import openai
import os

class AIRecommendationSystem:
    def __init__(self):
        self.db_name = "cs466_database.db"
        # Set OpenAI API key if available
        openai.api_key = os.getenv('OPENAI_API_KEY', 'demo-key')
    
    def analyze_student_performance(self, student_id: int) -> Dict:
        """Phân tích hiệu suất học tập của sinh viên"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        # L<PERSON>y thông tin sinh viên
        cursor.execute('SELECT username, full_name FROM users WHERE id = ?', (student_id,))
        student_info = cursor.fetchone()
        
        if not student_info:
            return {"error": "<PERSON>h viên không tồn tại"}
        
        username, full_name = student_info
        
        # Thống kê tổng quan
        cursor.execute('''
            SELECT 
                COUNT(DISTINCT a.id) as total_assignments,
                COUNT(DISTINCT s.assignment_id) as completed_assignments,
                AVG(s.score) as avg_score,
                COUNT(s.id) as total_submissions
            FROM assignments a
            LEFT JOIN submissions s ON a.id = s.assignment_id AND s.student_id = ?
        ''', (student_id,))
        
        stats = cursor.fetchone()
        total_assignments, completed_assignments, avg_score, total_submissions = stats
        
        # Phân tích theo ngôn ngữ
        cursor.execute('''
            SELECT 
                a.language,
                COUNT(DISTINCT s.assignment_id) as completed,
                AVG(s.score) as avg_score,
                COUNT(s.id) as submissions
            FROM assignments a
            LEFT JOIN submissions s ON a.id = s.assignment_id AND s.student_id = ?
            GROUP BY a.language
        ''', (student_id,))
        
        language_performance = {}
        for row in cursor.fetchall():
            language, completed, avg_score_lang, submissions = row
            language_performance[language] = {
                'completed': completed or 0,
                'avg_score': round(avg_score_lang or 0, 2),
                'submissions': submissions or 0
            }
        
        # Phân tích theo độ khó
        cursor.execute('''
            SELECT 
                a.difficulty,
                COUNT(DISTINCT s.assignment_id) as completed,
                AVG(s.score) as avg_score
            FROM assignments a
            LEFT JOIN submissions s ON a.id = s.assignment_id AND s.student_id = ?
            GROUP BY a.difficulty
        ''', (student_id,))
        
        difficulty_performance = {}
        for row in cursor.fetchall():
            difficulty, completed, avg_score_diff = row
            difficulty_performance[difficulty] = {
                'completed': completed or 0,
                'avg_score': round(avg_score_diff or 0, 2)
            }
        
        # Xu hướng học tập (30 ngày gần đây)
        cursor.execute('''
            SELECT 
                DATE(s.submitted_at) as date,
                COUNT(s.id) as submissions,
                AVG(s.score) as avg_score
            FROM submissions s
            WHERE s.student_id = ? 
            AND s.submitted_at >= datetime('now', '-30 days')
            GROUP BY DATE(s.submitted_at)
            ORDER BY date
        ''', (student_id,))
        
        learning_trend = []
        for row in cursor.fetchall():
            date, submissions, avg_score_day = row
            learning_trend.append({
                'date': date,
                'submissions': submissions,
                'avg_score': round(avg_score_day or 0, 2)
            })
        
        # Bài tập chưa hoàn thành
        cursor.execute('''
            SELECT a.id, a.title, a.language, a.difficulty, a.deadline
            FROM assignments a
            LEFT JOIN submissions s ON a.id = s.assignment_id AND s.student_id = ?
            WHERE s.id IS NULL
            ORDER BY a.deadline ASC
        ''', (student_id,))
        
        pending_assignments = []
        for row in cursor.fetchall():
            assignment_id, title, language, difficulty, deadline = row
            pending_assignments.append({
                'id': assignment_id,
                'title': title,
                'language': language,
                'difficulty': difficulty,
                'deadline': deadline
            })
        
        conn.close()
        
        # Tính toán các chỉ số
        completion_rate = (completed_assignments / total_assignments * 100) if total_assignments > 0 else 0
        
        return {
            'student_info': {
                'username': username,
                'full_name': full_name or username
            },
            'overall_stats': {
                'total_assignments': total_assignments,
                'completed_assignments': completed_assignments,
                'completion_rate': round(completion_rate, 2),
                'avg_score': round(avg_score or 0, 2),
                'total_submissions': total_submissions
            },
            'language_performance': language_performance,
            'difficulty_performance': difficulty_performance,
            'learning_trend': learning_trend,
            'pending_assignments': pending_assignments
        }
    
    def get_skill_level_assessment(self, student_id: int) -> Dict:
        """Đánh giá mức độ kỹ năng của sinh viên"""
        performance = self.analyze_student_performance(student_id)
        
        if 'error' in performance:
            return performance
        
        # Đánh giá tổng quan
        avg_score = performance['overall_stats']['avg_score']
        completion_rate = performance['overall_stats']['completion_rate']
        
        # Xác định level tổng quan
        if avg_score >= 85 and completion_rate >= 80:
            overall_level = "Xuất sắc"
            overall_color = "success"
        elif avg_score >= 70 and completion_rate >= 60:
            overall_level = "Khá"
            overall_color = "info"
        elif avg_score >= 60 and completion_rate >= 40:
            overall_level = "Trung bình"
            overall_color = "warning"
        else:
            overall_level = "Cần cải thiện"
            overall_color = "danger"
        
        # Đánh giá theo ngôn ngữ
        language_levels = {}
        for lang, perf in performance['language_performance'].items():
            if perf['avg_score'] >= 80:
                level = "Thành thạo"
                color = "success"
            elif perf['avg_score'] >= 65:
                level = "Khá"
                color = "info"
            elif perf['avg_score'] >= 50:
                level = "Cơ bản"
                color = "warning"
            else:
                level = "Mới bắt đầu"
                color = "danger"
            
            language_levels[lang] = {
                'level': level,
                'color': color,
                'score': perf['avg_score'],
                'completed': perf['completed']
            }
        
        # Đánh giá khả năng giải quyết vấn đề theo độ khó
        problem_solving = {}
        for difficulty, perf in performance['difficulty_performance'].items():
            if perf['avg_score'] >= 75:
                ability = "Tốt"
                color = "success"
            elif perf['avg_score'] >= 60:
                ability = "Khá"
                color = "info"
            elif perf['avg_score'] >= 45:
                ability = "Trung bình"
                color = "warning"
            else:
                ability = "Cần luyện tập"
                color = "danger"
            
            problem_solving[difficulty] = {
                'ability': ability,
                'color': color,
                'score': perf['avg_score'],
                'completed': perf['completed']
            }
        
        return {
            'overall_assessment': {
                'level': overall_level,
                'color': overall_color,
                'score': avg_score,
                'completion_rate': completion_rate
            },
            'language_skills': language_levels,
            'problem_solving_skills': problem_solving,
            'student_info': performance['student_info']
        }
    
    def generate_learning_recommendations(self, student_id: int) -> Dict:
        """Tạo gợi ý học tập cá nhân hóa"""
        performance = self.analyze_student_performance(student_id)
        
        if 'error' in performance:
            return performance
        
        recommendations = []
        
        # Gợi ý dựa trên completion rate
        completion_rate = performance['overall_stats']['completion_rate']
        if completion_rate < 50:
            recommendations.append({
                'type': 'urgent',
                'title': 'Hoàn thành bài tập cơ bản',
                'description': 'Bạn cần hoàn thành thêm các bài tập cơ bản để nắm vững kiến thức nền tảng.',
                'action': 'Làm bài tập độ khó "Dễ" trước',
                'priority': 'high'
            })
        
        # Gợi ý dựa trên điểm số
        avg_score = performance['overall_stats']['avg_score']
        if avg_score < 60:
            recommendations.append({
                'type': 'improvement',
                'title': 'Ôn tập kiến thức cơ bản',
                'description': 'Điểm số trung bình của bạn cho thấy cần ôn tập lại các khái niệm cơ bản.',
                'action': 'Xem lại lý thuyết và làm bài tập dễ',
                'priority': 'high'
            })
        
        # Gợi ý dựa trên ngôn ngữ
        for lang, perf in performance['language_performance'].items():
            if perf['avg_score'] < 50:
                recommendations.append({
                    'type': 'language_focus',
                    'title': f'Tăng cường luyện tập {lang.title()}',
                    'description': f'Kỹ năng {lang.title()} của bạn cần được cải thiện.',
                    'action': f'Làm thêm bài tập {lang.title()} cơ bản',
                    'priority': 'medium'
                })
            elif perf['avg_score'] >= 80:
                recommendations.append({
                    'type': 'advancement',
                    'title': f'Thử thách bản thân với {lang.title()} nâng cao',
                    'description': f'Bạn đã thành thạo {lang.title()} cơ bản, hãy thử các bài tập khó hơn.',
                    'action': f'Làm bài tập {lang.title()} độ khó "Khó"',
                    'priority': 'low'
                })
        
        # Gợi ý bài tập tiếp theo
        next_assignments = self._suggest_next_assignments(student_id, performance)
        
        return {
            'recommendations': recommendations,
            'next_assignments': next_assignments,
            'student_info': performance['student_info']
        }
    
    def _suggest_next_assignments(self, student_id: int, performance: Dict) -> List[Dict]:
        """Gợi ý bài tập tiếp theo phù hợp"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        # Xác định level hiện tại của sinh viên
        avg_score = performance['overall_stats']['avg_score']
        
        if avg_score >= 80:
            suggested_difficulty = ['medium', 'hard']
        elif avg_score >= 60:
            suggested_difficulty = ['easy', 'medium']
        else:
            suggested_difficulty = ['easy']
        
        # Tìm bài tập chưa làm phù hợp
        difficulty_placeholders = ','.join(['?' for _ in suggested_difficulty])
        
        cursor.execute(f'''
            SELECT a.id, a.title, a.language, a.difficulty, a.description, a.deadline
            FROM assignments a
            LEFT JOIN submissions s ON a.id = s.assignment_id AND s.student_id = ?
            WHERE s.id IS NULL 
            AND a.difficulty IN ({difficulty_placeholders})
            ORDER BY 
                CASE a.difficulty 
                    WHEN 'easy' THEN 1 
                    WHEN 'medium' THEN 2 
                    WHEN 'hard' THEN 3 
                END,
                a.deadline ASC
            LIMIT 5
        ''', [student_id] + suggested_difficulty)
        
        suggestions = []
        for row in cursor.fetchall():
            assignment_id, title, language, difficulty, description, deadline = row
            suggestions.append({
                'id': assignment_id,
                'title': title,
                'language': language,
                'difficulty': difficulty,
                'description': description,
                'deadline': deadline,
                'reason': self._get_suggestion_reason(difficulty, avg_score)
            })
        
        conn.close()
        return suggestions
    
    def _get_suggestion_reason(self, difficulty: str, avg_score: float) -> str:
        """Lý do gợi ý bài tập"""
        if difficulty == 'easy':
            if avg_score < 60:
                return "Củng cố kiến thức cơ bản"
            else:
                return "Ôn tập và làm quen với dạng bài mới"
        elif difficulty == 'medium':
            if avg_score >= 70:
                return "Phát triển kỹ năng trung cấp"
            else:
                return "Thử thách nhẹ để cải thiện"
        else:  # hard
            return "Thử thách bản thân với bài tập nâng cao"
    
    async def generate_ai_feedback(self, student_id: int) -> str:
        """Tạo phản hồi AI cá nhân hóa (mock implementation)"""
        performance = self.analyze_student_performance(student_id)
        
        if 'error' in performance:
            return "Không thể tạo phản hồi AI do lỗi dữ liệu."
        
        # Mock AI feedback - in production, this would call OpenAI API
        avg_score = performance['overall_stats']['avg_score']
        completion_rate = performance['overall_stats']['completion_rate']
        
        if avg_score >= 80 and completion_rate >= 80:
            feedback = f"Xuất sắc! Bạn đang có tiến bộ rất tốt với điểm trung bình {avg_score}% và tỷ lệ hoàn thành {completion_rate}%. Hãy tiếp tục thử thách bản thân với các bài tập khó hơn."
        elif avg_score >= 60:
            feedback = f"Khá tốt! Điểm số của bạn ({avg_score}%) cho thấy bạn đã nắm được kiến thức cơ bản. Hãy tập trung vào việc hoàn thành nhiều bài tập hơn để cải thiện tỷ lệ hoàn thành ({completion_rate}%)."
        else:
            feedback = f"Bạn cần cố gắng thêm! Điểm trung bình hiện tại là {avg_score}%. Hãy ôn tập lại lý thuyết và làm thêm các bài tập cơ bản để nâng cao kỹ năng."
        
        return feedback
