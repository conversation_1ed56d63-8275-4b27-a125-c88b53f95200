# CS466 Learning System - Git Ignore File

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Database files (optional - có thể keep để demo)
# *.db
# *.sqlite
# *.sqlite3

# Log files
*.log
logs/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Upload files (nếu có user uploads)
uploads/
static/uploads/

# Environment variables
.env
.env.local
.env.production
.env.staging

# Test files
.pytest_cache/
.coverage
htmlcov/
.tox/
.coverage.*
coverage.xml
*.cover
.hypothesis/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Custom temp files
test_*.py
*_test.py
debug_*.py
temp_*.py

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# FastAPI specific
.fastapi_cache/

# Keep these files for demo:
# - cs466_database.db (demo data)
# - learning_system.db (if exists)
# - final_system_check.py (testing tool) 