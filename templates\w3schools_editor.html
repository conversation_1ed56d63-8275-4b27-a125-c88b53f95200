<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CS466 Code Editor - W3Schools Style</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f1f1f1;
        }

        .header {
            background: #04AA6D;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 500;
        }

        .header .controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn-w3 {
            background: #04AA6D;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .btn-w3:hover {
            background: #059862;
            color: white;
        }

        .btn-w3.btn-run {
            background: #ff9800;
        }

        .btn-w3.btn-run:hover {
            background: #e68900;
        }

        .main-container {
            display: flex;
            height: calc(100vh - 60px);
        }

        .sidebar {
            width: 250px;
            background: white;
            border-right: 1px solid #ddd;
            overflow-y: auto;
        }

        .sidebar-section {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }

        .sidebar-section h6 {
            margin: 0 0 10px 0;
            color: #333;
            font-weight: 600;
        }

        .language-selector {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }

        .ai-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            margin-top: 10px;
        }

        .ai-suggestion {
            background: white;
            border: 1px solid #e3f2fd;
            border-radius: 6px;
            padding: 10px;
            margin: 8px 0;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .ai-suggestion:hover {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .editor-container {
            flex: 1;
            display: flex;
        }

        .code-panel {
            flex: 1;
            background: white;
            border-right: 1px solid #ddd;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            background: #f8f9fa;
            padding: 10px 15px;
            border-bottom: 1px solid #ddd;
            font-weight: 500;
            color: #333;
        }

        .code-editor {
            flex: 1;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            border: none;
            outline: none;
            padding: 15px;
            resize: none;
            background: #1e1e1e;
            color: #d4d4d4;
        }

        .output-panel {
            width: 40%;
            background: white;
            display: flex;
            flex-direction: column;
        }

        .output-content {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            background: #f8f9fa;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
        }

        .examples-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .example-item {
            padding: 8px 12px;
            border: 1px solid #eee;
            border-radius: 4px;
            margin: 5px 0;
            cursor: pointer;
            font-size: 13px;
            background: white;
            transition: all 0.2s;
        }

        .example-item:hover {
            background: #f0f8ff;
            border-color: #2196f3;
        }

        .status-bar {
            background: #f8f9fa;
            padding: 8px 15px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
            display: flex;
            justify-content: space-between;
        }

        .ai-loading {
            display: none;
            text-align: center;
            padding: 10px;
            color: #666;
        }

        .output-success {
            color: #28a745;
        }

        .output-error {
            color: #dc3545;
        }

        .output-info {
            color: #17a2b8;
        }

        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
                max-height: 200px;
            }
            
            .editor-container {
                flex-direction: column;
            }
            
            .output-panel {
                width: 100%;
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1><i class="fas fa-code"></i> CS466 Code Editor</h1>
        <div class="controls">
            <select id="language-select" class="language-selector me-2">
                <option value="python">Python</option>
                <option value="perl">Perl</option>
            </select>
            <button class="btn-w3 btn-run" onclick="runCode()">
                <i class="fas fa-play"></i> Run
            </button>
            <button class="btn-w3" onclick="clearCode()">
                <i class="fas fa-trash"></i> Clear
            </button>
            <button class="btn-w3" onclick="saveCode()">
                <i class="fas fa-save"></i> Save
            </button>
        </div>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Language Examples -->
            <div class="sidebar-section">
                <h6><i class="fas fa-book"></i> Examples</h6>
                <div class="examples-list" id="examples-list">
                    <!-- Examples will be loaded here -->
                </div>
            </div>

            <!-- AI Assistant -->
            <div class="sidebar-section">
                <h6><i class="fas fa-robot"></i> AI Assistant</h6>
                <div class="ai-panel">
                    <input type="text" class="form-control form-control-sm mb-2" 
                           id="ai-prompt" placeholder="Ask AI for help...">
                    <button class="btn btn-sm btn-primary w-100" onclick="getAISuggestion()">
                        <i class="fas fa-magic"></i> Get Suggestion
                    </button>
                    <div class="ai-loading" id="ai-loading">
                        <i class="fas fa-spinner fa-spin"></i> AI is thinking...
                    </div>
                    <div id="ai-suggestions"></div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="sidebar-section">
                <h6><i class="fas fa-bolt"></i> Quick Actions</h6>
                <button class="btn btn-sm btn-outline-primary w-100 mb-2" onclick="formatCode()">
                    <i class="fas fa-indent"></i> Format Code
                </button>
                <button class="btn btn-sm btn-outline-secondary w-100 mb-2" onclick="shareCode()">
                    <i class="fas fa-share"></i> Share Code
                </button>
                <button class="btn btn-sm btn-outline-info w-100" onclick="downloadCode()">
                    <i class="fas fa-download"></i> Download
                </button>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <div class="editor-container">
                <!-- Code Panel -->
                <div class="code-panel">
                    <div class="panel-header">
                        <i class="fas fa-edit"></i> Code Editor
                        <span class="ms-2" id="language-indicator">Python</span>
                    </div>
                    <textarea class="code-editor" id="code-editor" placeholder="Write your code here..."></textarea>
                </div>

                <!-- Output Panel -->
                <div class="output-panel">
                    <div class="panel-header">
                        <i class="fas fa-terminal"></i> Output
                        <span class="ms-2" id="execution-time"></span>
                    </div>
                    <div class="output-content" id="output-content">
                        <div class="text-center text-muted">
                            <i class="fas fa-play-circle fa-2x mb-2"></i>
                            <p>Click <strong>Run</strong> to execute your code</p>
                            <p>Use the AI Assistant for help and suggestions</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Bar -->
            <div class="status-bar">
                <span id="status-text">Ready</span>
                <span id="cursor-position">Line 1, Column 1</span>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        const token = localStorage.getItem('access_token');
        let currentLanguage = 'python';

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadExamples();
            setupEventListeners();
            loadDefaultCode();
        });

        function setupEventListeners() {
            // Language selector
            document.getElementById('language-select').addEventListener('change', function() {
                currentLanguage = this.value;
                document.getElementById('language-indicator').textContent =
                    currentLanguage.charAt(0).toUpperCase() + currentLanguage.slice(1);
                loadExamples();
                loadDefaultCode();
            });

            // Code editor events
            const editor = document.getElementById('code-editor');
            editor.addEventListener('input', updateStatus);
            editor.addEventListener('keyup', updateCursorPosition);
            editor.addEventListener('click', updateCursorPosition);

            // AI prompt enter key
            document.getElementById('ai-prompt').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    getAISuggestion();
                }
            });
        }

        function loadDefaultCode() {
            const editor = document.getElementById('code-editor');

            if (currentLanguage === 'python') {
                editor.value = `# Welcome to CS466 Python Editor
# Try the examples or ask AI for help!

def main():
    print("Hello, World!")

    # Example: Simple calculation
    a = 10
    b = 20
    result = a + b
    print(f"The sum of {a} and {b} is {result}")

if __name__ == "__main__":
    main()`;
            } else if (currentLanguage === 'perl') {
                editor.value = `#!/usr/bin/perl
# Welcome to CS466 Perl Editor
# Try the examples or ask AI for help!

use strict;
use warnings;

print "Hello, World!\\n";

# Example: Simple calculation
my $a = 10;
my $b = 20;
my $result = $a + $b;
print "The sum of $a and $b is $result\\n";`;
            }

            updateStatus();
        }

        function loadExamples() {
            const examplesList = document.getElementById('examples-list');
            let examples = [];

            if (currentLanguage === 'python') {
                examples = [
                    {
                        title: "Hello World",
                        code: `print("Hello, World!")`
                    },
                    {
                        title: "Variables & Input",
                        code: `name = input("Enter your name: ")
age = int(input("Enter your age: "))
print(f"Hello {name}, you are {age} years old!")`
                    },
                    {
                        title: "For Loop",
                        code: `for i in range(1, 6):
    print(f"Number: {i}")`
                    },
                    {
                        title: "Function",
                        code: `def greet(name):
    return f"Hello, {name}!"

result = greet("Python")
print(result)`
                    },
                    {
                        title: "List Operations",
                        code: `numbers = [1, 2, 3, 4, 5]
squares = [x**2 for x in numbers]
print("Original:", numbers)
print("Squares:", squares)`
                    }
                ];
            } else if (currentLanguage === 'perl') {
                examples = [
                    {
                        title: "Hello World",
                        code: `#!/usr/bin/perl
print "Hello, World!\\n";`
                    },
                    {
                        title: "Variables",
                        code: `#!/usr/bin/perl
my $name = "Perl";
my $version = 5.32;
print "Language: $name, Version: $version\\n";`
                    },
                    {
                        title: "Arrays",
                        code: `#!/usr/bin/perl
my @fruits = ("apple", "banana", "orange");
foreach my $fruit (@fruits) {
    print "Fruit: $fruit\\n";
}`
                    },
                    {
                        title: "Subroutine",
                        code: `#!/usr/bin/perl
sub greet {
    my $name = shift;
    return "Hello, $name!";
}

my $message = greet("Perl");
print "$message\\n";`
                    }
                ];
            }

            examplesList.innerHTML = examples.map(example => `
                <div class="example-item" onclick="loadExample('${example.title}', \`${example.code.replace(/`/g, '\\`')}\`)">
                    <strong>${example.title}</strong>
                </div>
            `).join('');
        }

        function loadExample(title, code) {
            document.getElementById('code-editor').value = code;
            updateStatus();
            document.getElementById('status-text').textContent = `Loaded example: ${title}`;
        }

        async function runCode() {
            const code = document.getElementById('code-editor').value.trim();
            const outputDiv = document.getElementById('output-content');

            if (!code) {
                outputDiv.innerHTML = '<div class="output-error">Please enter some code first!</div>';
                return;
            }

            // Show loading
            outputDiv.innerHTML = '<div class="output-info"><i class="fas fa-spinner fa-spin"></i> Running code...</div>';
            document.getElementById('status-text').textContent = 'Executing...';

            const startTime = Date.now();

            try {
                const formData = new FormData();
                formData.append('code', code);
                formData.append('language', currentLanguage);

                const response = await fetch('/code/run', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const result = await response.json();
                const endTime = Date.now();
                const executionTime = endTime - startTime;

                document.getElementById('execution-time').innerHTML =
                    `<small class="text-muted">Execution: ${executionTime}ms</small>`;

                if (result.success) {
                    displayOutput(result.result, executionTime);
                } else {
                    outputDiv.innerHTML = `<div class="output-error">
                        <strong>Error:</strong> ${result.result?.error || 'Unknown error'}
                    </div>`;
                }
            } catch (error) {
                outputDiv.innerHTML = `<div class="output-error">
                    <strong>Connection Error:</strong> ${error.message}
                </div>`;
            }

            document.getElementById('status-text').textContent = 'Ready';
        }

        function displayOutput(result, executionTime) {
            const outputDiv = document.getElementById('output-content');
            let html = '';

            if (result.output) {
                html += `<div class="output-success"><strong>Output:</strong>\n${result.output}</div>`;
            }

            if (result.error) {
                html += `<div class="output-error"><strong>Error:</strong>\n${result.error}</div>`;
            }

            if (!result.output && !result.error) {
                html = '<div class="output-info">Code executed successfully (no output)</div>';
            }

            outputDiv.innerHTML = html;
        }

        async function getAISuggestion() {
            const prompt = document.getElementById('ai-prompt').value.trim();
            const code = document.getElementById('code-editor').value;
            const suggestionsDiv = document.getElementById('ai-suggestions');
            const loadingDiv = document.getElementById('ai-loading');

            if (!prompt) {
                alert('Please enter a question or request for AI assistance');
                return;
            }

            // Show loading
            loadingDiv.style.display = 'block';
            suggestionsDiv.innerHTML = '';

            try {
                const response = await fetch('/api/ai/code-suggestion', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        code: code,
                        language: currentLanguage
                    })
                });

                const result = await response.json();
                loadingDiv.style.display = 'none';

                if (result.success) {
                    displayAISuggestions(result.suggestions);
                } else {
                    suggestionsDiv.innerHTML = `
                        <div class="ai-suggestion" style="border-color: #dc3545; background: #f8d7da;">
                            <strong>AI Error:</strong> ${result.error || 'Unable to get AI suggestion'}
                        </div>
                    `;
                }
            } catch (error) {
                loadingDiv.style.display = 'none';
                suggestionsDiv.innerHTML = `
                    <div class="ai-suggestion" style="border-color: #dc3545; background: #f8d7da;">
                        <strong>Connection Error:</strong> ${error.message}
                    </div>
                `;
            }

            // Clear prompt
            document.getElementById('ai-prompt').value = '';
        }

        function displayAISuggestions(suggestions) {
            const suggestionsDiv = document.getElementById('ai-suggestions');

            if (!suggestions || suggestions.length === 0) {
                suggestionsDiv.innerHTML = `
                    <div class="ai-suggestion">
                        <strong>No suggestions available</strong>
                        <p>Try asking a more specific question about your code.</p>
                    </div>
                `;
                return;
            }

            const html = suggestions.map(suggestion => `
                <div class="ai-suggestion" onclick="applySuggestion(\`${suggestion.code?.replace(/`/g, '\\`') || ''}\`)">
                    <strong>${suggestion.title || 'AI Suggestion'}</strong>
                    <p>${suggestion.description || suggestion.text || ''}</p>
                    ${suggestion.code ? `<small class="text-muted">Click to apply code</small>` : ''}
                </div>
            `).join('');

            suggestionsDiv.innerHTML = html;
        }

        function applySuggestion(code) {
            if (code) {
                const editor = document.getElementById('code-editor');
                const currentCode = editor.value;

                if (confirm('Replace current code with AI suggestion?')) {
                    editor.value = code;
                } else {
                    // Append to current code
                    editor.value = currentCode + '\n\n# AI Suggestion:\n' + code;
                }
                updateStatus();
            }
        }

        function clearCode() {
            if (confirm('Are you sure you want to clear all code?')) {
                document.getElementById('code-editor').value = '';
                document.getElementById('output-content').innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-code fa-2x mb-2"></i>
                        <p>Code cleared. Start writing your code!</p>
                    </div>
                `;
                updateStatus();
            }
        }

        function saveCode() {
            const code = document.getElementById('code-editor').value;
            const language = currentLanguage;
            const filename = `code.${language === 'python' ? 'py' : 'pl'}`;

            const blob = new Blob([code], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            window.URL.revokeObjectURL(url);

            document.getElementById('status-text').textContent = `Code saved as ${filename}`;
        }

        function formatCode() {
            // Simple code formatting
            const editor = document.getElementById('code-editor');
            let code = editor.value;

            if (currentLanguage === 'python') {
                // Basic Python formatting
                code = code.replace(/\t/g, '    '); // Replace tabs with 4 spaces
                code = code.replace(/\s+$/gm, ''); // Remove trailing spaces
            }

            editor.value = code;
            document.getElementById('status-text').textContent = 'Code formatted';
        }

        function shareCode() {
            const code = document.getElementById('code-editor').value;
            if (navigator.share) {
                navigator.share({
                    title: 'CS466 Code',
                    text: code
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(code).then(() => {
                    alert('Code copied to clipboard!');
                });
            }
        }

        function downloadCode() {
            saveCode(); // Same as save function
        }

        function updateStatus() {
            const code = document.getElementById('code-editor').value;
            const lines = code.split('\n').length;
            const chars = code.length;
            document.getElementById('status-text').textContent = `${lines} lines, ${chars} characters`;
        }

        function updateCursorPosition() {
            const editor = document.getElementById('code-editor');
            const start = editor.selectionStart;
            const text = editor.value.substring(0, start);
            const lines = text.split('\n');
            const line = lines.length;
            const column = lines[lines.length - 1].length + 1;

            document.getElementById('cursor-position').textContent = `Line ${line}, Column ${column}`;
        }
    </script>
</body>
</html>
