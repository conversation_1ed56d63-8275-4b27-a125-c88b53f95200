<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 AI Question Generator - CS466 Learning System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Inter', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 2rem;
            overflow: hidden;
        }
        
        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header-section h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .header-section p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 0;
        }
        
        .content-section {
            padding: 2rem;
        }
        
        .generator-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .generator-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            border: none;
            border-radius: 10px;
            padding: 0.5rem 1.5rem;
            font-weight: 600;
        }
        
        .btn-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border: none;
            border-radius: 10px;
            padding: 0.5rem 1.5rem;
            font-weight: 600;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            border: none;
            border-radius: 10px;
            padding: 0.5rem 1.5rem;
            font-weight: 600;
        }
        
        .question-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
        }
        
        .question-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .question-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        .question-meta {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .meta-badge {
            background: #f8f9fa;
            border-radius: 20px;
            padding: 0.3rem 0.8rem;
            font-size: 0.85rem;
            font-weight: 500;
            color: #6c757d;
        }
        
        .meta-badge.difficulty-easy {
            background: #d4edda;
            color: #155724;
        }
        
        .meta-badge.difficulty-medium {
            background: #fff3cd;
            color: #856404;
        }
        
        .meta-badge.difficulty-hard {
            background: #f8d7da;
            color: #721c24;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
            white-space: pre-wrap;
            margin: 1rem 0;
        }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        
        .loading-spinner .spinner-border {
            width: 3rem;
            height: 3rem;
            color: #667eea;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .navigation-breadcrumb {
            background: transparent;
            padding: 1rem 0;
        }
        
        .breadcrumb-item a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .breadcrumb-item.active {
            color: #6c757d;
        }
        
        .alert {
            border: none;
            border-radius: 10px;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
            color: #721c24;
        }
        
        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            border-bottom: none;
        }
        
        .modal-title {
            font-weight: 600;
        }
        
        .btn-close {
            filter: invert(1);
        }
        
        .topic-selection {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .topic-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .topic-card:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.1);
        }
        
        .topic-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .topic-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .topic-name {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .topic-description {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .generation-history {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .history-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }
        
        .history-meta {
            font-size: 0.85rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        
        .history-stats {
            display: flex;
            gap: 1rem;
            font-size: 0.9rem;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            color: #667eea !important;
        }
        
        .nav-link {
            color: #495057 !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .nav-link:hover {
            color: #667eea !important;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in {
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateX(-20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="/dashboard/teacher">
                <i class="fas fa-graduation-cap"></i> CS466 Learning System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard/teacher">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a class="nav-link" href="/assignment/create">
                    <i class="fas fa-plus"></i> Tạo bài tập
                </a>
                <a class="nav-link active" href="/ai/question-generator">
                    <i class="fas fa-robot"></i> AI Generator
                </a>
                <a class="nav-link" href="/" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> Đăng xuất
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <h1><i class="fas fa-robot"></i> AI Question Generator</h1>
            <p>Tạo câu hỏi Python tự động bằng trí tuệ nhân tạo</p>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <!-- Breadcrumb -->
            <nav class="navigation-breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/dashboard/teacher">Dashboard</a></li>
                    <li class="breadcrumb-item active">AI Question Generator</li>
                </ol>
            </nav>

            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number" id="total-questions">0</div>
                        <div class="stats-label">Câu hỏi đã tạo</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number" id="ai-questions">0</div>
                        <div class="stats-label">Câu hỏi AI</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number" id="assignments-created">0</div>
                        <div class="stats-label">Bài tập đã tạo</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number" id="avg-generation-time">0s</div>
                        <div class="stats-label">Thời gian TB</div>
                    </div>
                </div>
            </div>

            <!-- Generator Controls -->
            <div class="generator-card fade-in">
                <h3><i class="fas fa-magic"></i> Tạo câu hỏi mới</h3>
                <p class="text-muted">Chọn chủ đề và cấu hình để tạo câu hỏi Python tự động</p>
                
                <form id="generator-form">
                    <!-- Topic Selection -->
                    <div class="mb-4">
                        <label class="form-label">Chủ đề</label>
                        <div class="topic-selection" id="topic-selection">
                            <!-- Topics will be loaded here -->
                        </div>
                        <input type="hidden" id="selected-topic" name="topic">
                    </div>

                    <!-- Configuration -->
                    <div class="row">
                        <div class="col-md-4">
                            <label for="difficulty" class="form-label">Độ khó</label>
                            <select class="form-select" id="difficulty" name="difficulty">
                                <option value="easy">Dễ</option>
                                <option value="medium" selected>Trung bình</option>
                                <option value="hard">Khó</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="count" class="form-label">Số lượng</label>
                            <select class="form-select" id="count" name="count">
                                <option value="1">1 câu hỏi</option>
                                <option value="3" selected>3 câu hỏi</option>
                                <option value="5">5 câu hỏi</option>
                                <option value="10">10 câu hỏi</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="use-ai" class="form-label">Phương thức</label>
                            <select class="form-select" id="use-ai" name="use_ai">
                                <option value="true" selected>Sử dụng AI</option>
                                <option value="false">Câu hỏi có sẵn</option>
                            </select>
                        </div>
                    </div>

                    <!-- Generate Button -->
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-magic"></i> Tạo câu hỏi
                        </button>
                    </div>
                </form>
            </div>

            <!-- Loading Spinner -->
            <div class="loading-spinner" id="loading-spinner">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Đang tạo câu hỏi...</span>
                </div>
                <p class="mt-3">Đang tạo câu hỏi bằng AI...</p>
            </div>

            <!-- Alert Messages -->
            <div id="alert-container"></div>

            <!-- Generated Questions -->
            <div id="questions-container" class="fade-in" style="display: none;">
                <h3><i class="fas fa-list"></i> Câu hỏi đã tạo</h3>
                <div id="questions-list"></div>
            </div>

            <!-- Question Library -->
            <div class="generator-card slide-in">
                <h3><i class="fas fa-book"></i> Thư viện câu hỏi</h3>
                <p class="text-muted">Xem và quản lý các câu hỏi đã tạo trước đó</p>
                
                <div class="row mb-3">
                    <div class="col-md-4">
                        <select class="form-select" id="filter-topic">
                            <option value="">Tất cả chủ đề</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" id="filter-difficulty">
                            <option value="">Tất cả độ khó</option>
                            <option value="easy">Dễ</option>
                            <option value="medium">Trung bình</option>
                            <option value="hard">Khó</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-info" onclick="loadQuestionLibrary()">
                            <i class="fas fa-search"></i> Tìm kiếm
                        </button>
                    </div>
                </div>
                
                <div id="question-library" class="generation-history">
                    <!-- Questions will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Question Detail Modal -->
    <div class="modal fade" id="questionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Chi tiết câu hỏi</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="question-detail">
                    <!-- Question details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                    <button type="button" class="btn btn-success" onclick="createAssignmentFromQuestion()">
                        <i class="fas fa-plus"></i> Tạo bài tập
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Global variables
        let currentUser = null;
        let token = localStorage.getItem('token');
        let selectedQuestion = null;
        let availableTopics = {};

        // Initialize page directly - no authentication required
        loadTopics();
        loadStats();
        loadQuestionLibrary();

        // Verify authentication
        async function verifyAuthentication() {
            try {
                // Try a simpler endpoint that doesn't require strict auth
                const response = await fetch('/ai/topics', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (!response.ok) {
                    // If auth fails, still allow access but show warning
                    console.warn('Authentication check failed, proceeding with limited access');
                    showAlert('Đăng nhập để sử dụng đầy đủ tính năng', 'warning');
                }
                
                // Initialize page regardless of auth status
                loadTopics();
                loadStats();
                loadQuestionLibrary();
            } catch (error) {
                console.error('Auth verification failed:', error);
                // Still proceed with page initialization
                loadTopics();
                loadStats();
                loadQuestionLibrary();
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Authentication check is done in global scope
        });

        // Show alert function
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.container-fluid');
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto dismiss after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // Load available topics
        async function loadTopics() {
            try {
                const response = await fetch('/ai/topics');
                const result = await response.json();
                
                if (result.success) {
                    availableTopics = result.topics;
                    displayTopics(result.topics);
                    populateFilterOptions(result.topics);
                } else {
                    // Fallback topics if API fails
                    loadFallbackTopics();
                }
            } catch (error) {
                console.error('Error loading topics:', error);
                // Fallback topics if API fails
                loadFallbackTopics();
            }
        }

        // Load fallback topics
        function loadFallbackTopics() {
            const fallbackTopics = {
                'basic_syntax': {
                    'name': 'Cú pháp cơ bản',
                    'description': 'Variables, operators, basic I/O'
                },
                'conditionals': {
                    'name': 'Câu lệnh điều kiện',
                    'description': 'if/else, logical operators'
                },
                'loops': {
                    'name': 'Vòng lặp',
                    'description': 'for, while loops, nested loops'
                },
                'functions': {
                    'name': 'Hàm',
                    'description': 'Function definition, parameters, return values'
                },
                'data_structures': {
                    'name': 'Cấu trúc dữ liệu',
                    'description': 'Lists, dictionaries, tuples'
                },
                'algorithms': {
                    'name': 'Thuật toán',
                    'description': 'Sorting, searching, recursion'
                }
            };
            
            availableTopics = fallbackTopics;
            displayTopics(fallbackTopics);
            populateFilterOptions(fallbackTopics);
        }

        // Display topics for selection
        function displayTopics(topics) {
            const container = document.getElementById('topic-selection');
            const topicIcons = {
                'basic_syntax': 'fas fa-code',
                'conditionals': 'fas fa-code-branch',
                'loops': 'fas fa-sync-alt',
                'functions': 'fas fa-function',
                'data_structures': 'fas fa-database',
                'algorithms': 'fas fa-brain'
            };
            
            container.innerHTML = Object.entries(topics).map(([key, topic]) => 
                `<div class="topic-card" onclick="selectTopic('${key}')">
                    <div class="topic-icon">
                        <i class="${topicIcons[key] || 'fas fa-book'}"></i>
                    </div>
                    <div class="topic-name">${topic.name}</div>
                    <div class="topic-description">${topic.description}</div>
                </div>`
            ).join('');
        }

        // Select topic
        function selectTopic(topicKey) {
            // Remove previous selection
            document.querySelectorAll('.topic-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Add selection to clicked card
            event.target.closest('.topic-card').classList.add('selected');
            
            // Set hidden input
            document.getElementById('selected-topic').value = topicKey;
        }

        // Populate filter options
        function populateFilterOptions(topics) {
            const filterTopic = document.getElementById('filter-topic');
            filterTopic.innerHTML = '<option value="">Tất cả chủ đề</option>' +
                Object.entries(topics).map(([key, topic]) => 
                    `<option value="${key}">${topic.name}</option>`
                ).join('');
        }

        // Load statistics
        async function loadStats() {
            try {
                // Use mock data for now since auth is not working
                document.getElementById('total-questions').textContent = '0';
                document.getElementById('ai-questions').textContent = '0';
                document.getElementById('assignments-created').textContent = '0';
                document.getElementById('avg-generation-time').textContent = '0s';
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Handle form submission
        document.getElementById('generator-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const topic = document.getElementById('selected-topic').value;
            const difficulty = document.getElementById('difficulty').value;
            const count = parseInt(document.getElementById('count').value);
            const useAi = document.getElementById('use-ai').value === 'true';
            
            console.log('Form data:', { topic, difficulty, count, useAi });
            
            if (!topic) {
                showAlert('Vui lòng chọn chủ đề!', 'danger');
                return;
            }
            
            // Show loading
            document.getElementById('loading-spinner').style.display = 'block';
            document.getElementById('questions-container').style.display = 'none';
            
            try {
                const formData = new FormData();
                formData.append('topic', topic);
                formData.append('difficulty', difficulty);
                formData.append('count', count);
                formData.append('use_ai', useAi);
                
                const response = await fetch('/ai/generate-multiple-questions', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert(`Đã tạo thành công ${result.generated_count} câu hỏi trong ${result.generation_time}s!`, 'success');
                    displayGeneratedQuestions(result.questions);
                    loadStats(); // Refresh stats
                    loadQuestionLibrary(); // Refresh library
                } else {
                    showAlert(`Lỗi: ${result.error}`, 'danger');
                }
            } catch (error) {
                showAlert(`Lỗi kết nối: ${error.message}`, 'danger');
            } finally {
                document.getElementById('loading-spinner').style.display = 'none';
            }
        });

        // Display generated questions
        function displayGeneratedQuestions(questions) {
            const container = document.getElementById('questions-list');
            
            container.innerHTML = questions.map(question => `
                <div class="question-card">
                    <div class="question-title">${question.title}</div>
                    <div class="question-meta">
                        <span class="meta-badge">${availableTopics[question.topic]?.name || question.topic}</span>
                        <span class="meta-badge difficulty-${question.difficulty}">${getDifficultyText(question.difficulty)}</span>
                        <span class="meta-badge">${question.generated_by === 'ai' ? 'AI Generated' : 'Template'}</span>
                    </div>
                    <p class="text-muted">${question.description}</p>
                    <div class="d-flex gap-2">
                        <button class="btn btn-info btn-sm" onclick="viewQuestion(${question.id})">
                            <i class="fas fa-eye"></i> Xem chi tiết
                        </button>
                        <button class="btn btn-success btn-sm" onclick="createAssignment(${question.id})">
                            <i class="fas fa-plus"></i> Tạo bài tập
                        </button>
                    </div>
                </div>
            `).join('');
            
            document.getElementById('questions-container').style.display = 'block';
        }

        // Load question library
        async function loadQuestionLibrary() {
            try {
                // Show empty state for now
                document.getElementById('question-library').innerHTML = 
                    '<div class="no-data"><i class="fas fa-robot"></i><h4>Chưa có câu hỏi nào</h4><p>Tạo câu hỏi đầu tiên của bạn</p></div>';
            } catch (error) {
                console.error('Error loading question library:', error);
                document.getElementById('question-library').innerHTML = 
                    '<div class="no-data"><i class="fas fa-robot"></i><h4>Chưa có câu hỏi nào</h4><p>Tạo câu hỏi đầu tiên của bạn</p></div>';
            }
        }

        // Display question library
        function displayQuestionLibrary(questions) {
            const container = document.getElementById('question-library');
            
            if (questions.length === 0) {
                container.innerHTML = '<p class="text-muted text-center">Chưa có câu hỏi nào.</p>';
                return;
            }
            
            container.innerHTML = questions.map(question => `
                <div class="history-item">
                    <div class="history-meta">
                        <i class="fas fa-clock"></i> ${formatDate(question.created_at)} | 
                        <i class="fas fa-user"></i> ${question.teacher_name} |
                        <i class="fas fa-chart-line"></i> Đã dùng ${question.usage_count} lần
                    </div>
                    <h6>${question.title}</h6>
                    <div class="history-stats">
                        <span class="meta-badge">${availableTopics[question.topic]?.name || question.topic}</span>
                        <span class="meta-badge difficulty-${question.difficulty}">${getDifficultyText(question.difficulty)}</span>
                        <span class="meta-badge">${question.generated_by === 'ai' ? 'AI' : 'Template'}</span>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-info btn-sm" onclick="viewQuestion(${question.id})">
                            <i class="fas fa-eye"></i> Xem
                        </button>
                        <button class="btn btn-success btn-sm" onclick="createAssignment(${question.id})">
                            <i class="fas fa-plus"></i> Tạo bài tập
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // View question details
        async function viewQuestion(questionId) {
            try {
                const response = await fetch(`/ai/questions?limit=1000`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const question = result.questions.find(q => q.id === questionId);
                    if (question) {
                        selectedQuestion = question;
                        displayQuestionDetail(question);
                        new bootstrap.Modal(document.getElementById('questionModal')).show();
                    }
                }
            } catch (error) {
                console.error('Error viewing question:', error);
            }
        }

        // Display question detail in modal
        function displayQuestionDetail(question) {
            const container = document.getElementById('question-detail');
            
            container.innerHTML = `
                <div class="mb-3">
                    <h5>${question.title}</h5>
                    <div class="question-meta mb-3">
                        <span class="meta-badge">${availableTopics[question.topic]?.name || question.topic}</span>
                        <span class="meta-badge difficulty-${question.difficulty}">${getDifficultyText(question.difficulty)}</span>
                        <span class="meta-badge">${question.generated_by === 'ai' ? 'AI Generated' : 'Template'}</span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <h6>Mô tả:</h6>
                    <p>${question.description}</p>
                </div>
                
                <div class="mb-3">
                    <h6>Code khởi tạo:</h6>
                    <div class="code-block">${question.starter_code}</div>
                </div>
                
                <div class="mb-3">
                    <h6>Lời giải:</h6>
                    <div class="code-block">${question.solution}</div>
                </div>
                
                <div class="mb-3">
                    <h6>Test cases:</h6>
                    ${question.test_cases.map((test, index) => `
                        <div class="mb-2">
                            <strong>Test ${index + 1}:</strong><br>
                            Input: ${JSON.stringify(test.input)}<br>
                            Output: ${test.expected_output}
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // Create assignment from question
        async function createAssignment(questionId) {
            if (!questionId && selectedQuestion) {
                questionId = selectedQuestion.id;
            }
            
            const title = prompt('Nhập tiêu đề bài tập:', selectedQuestion?.title || '');
            if (!title) return;
            
            const days = prompt('Deadline (số ngày từ hôm nay):', '7');
            if (!days) return;
            
            try {
                const formData = new FormData();
                formData.append('title', title);
                formData.append('deadline_days', days);
                
                const response = await fetch(`/ai/question/${questionId}/create-assignment`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert('Đã tạo bài tập thành công!', 'success');
                    // Close modal if open
                    const modal = bootstrap.Modal.getInstance(document.getElementById('questionModal'));
                    if (modal) modal.hide();
                    
                    // Refresh stats
                    loadStats();
                } else {
                    showAlert(`Lỗi: ${result.error}`, 'danger');
                }
            } catch (error) {
                showAlert(`Lỗi kết nối: ${error.message}`, 'danger');
            }
        }

        // Create assignment from question (for modal)
        function createAssignmentFromQuestion() {
            if (selectedQuestion) {
                createAssignment(selectedQuestion.id);
            }
        }

        // Utility functions
        function showAlert(message, type) {
            const container = document.getElementById('alert-container');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            container.appendChild(alert);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }

        function getDifficultyText(difficulty) {
            const difficultyMap = {
                'easy': 'Dễ',
                'medium': 'Trung bình',
                'hard': 'Khó'
            };
            return difficultyMap[difficulty] || difficulty;
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('vi-VN') + ' ' + date.toLocaleTimeString('vi-VN');
        }

        function logout() {
            localStorage.removeItem('token');
            window.location.href = '/';
        }
    </script>
</body>
</html> 