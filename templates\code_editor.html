<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Editor - CS466 Learning System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --border-radius: 8px;
            --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .editor-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }

        .editor-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1rem 1.5rem;
            border-bottom: none;
        }

        .editor-header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .editor-toolbar {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }

        .code-workspace {
            display: flex;
            height: 600px;
        }

        .code-panel {
            flex: 1;
            border-right: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
        }

        .output-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            background: #f8f9fa;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
        }

        .panel-content {
            flex: 1;
            overflow: hidden;
        }

        #monaco-editor {
            height: 100%;
        }

        .output-content {
            height: 100%;
            overflow-y: auto;
            padding: 1rem;
            background: #f8f9fa;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        .btn-run {
            background: var(--success-color);
            color: white;
            border: none;
        }

        .btn-run:hover {
            background: #218838;
            color: white;
        }

        .btn-interactive {
            background: var(--primary-color);
            color: white;
            border: none;
        }

        .btn-interactive:hover {
            background: #5a6fd8;
            color: white;
        }

        .execution-info {
            font-size: 0.875rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }

        .code-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: var(--border-radius);
            padding: 1rem;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.875rem;
            line-height: 1.4;
        }

        .code-output pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .navbar-brand {
            font-weight: 600;
            color: white !important;
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.8) !important;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
        }

        @media (max-width: 768px) {
            .code-workspace {
                flex-direction: column;
                height: auto;
            }
            
            .code-panel {
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }
            
            .panel-content {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: rgba(0,0,0,0.1);">
        <div class="container">
            <a class="navbar-brand" href="/dashboard/student">
                <i class="fas fa-graduation-cap"></i> CS466 - Code Editor
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/" id="home-link">
                            <i class="fas fa-home"></i> Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard/student" id="dashboard-link" style="display: none;">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="logout()" id="logout-link" style="display: none;">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container main-container">
        <div class="editor-card">
            <!-- Header -->
            <div class="editor-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h1><i class="fas fa-code"></i> Online Code Editor</h1>
                    <div class="d-flex align-items-center">
                        <small class="me-3">Powered by Monaco Editor</small>
                        <span class="badge bg-light text-dark">Python 3.8+</span>
                    </div>
                </div>
            </div>

            <!-- Toolbar -->
            <div class="editor-toolbar">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <label class="form-label me-2 mb-0">Language:</label>
                            <select class="form-select form-select-sm" id="language-select" style="width: auto;">
                                <option value="python">Python</option>
                                <option value="perl">Perl</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="d-flex justify-content-end">
                            <div class="btn-group me-2">
                                <button class="btn btn-run btn-sm" onclick="runCode()">
                                    <i class="fas fa-play"></i> Run
                                </button>
                                <button class="btn btn-interactive btn-sm" onclick="runInteractiveMode()">
                                    <i class="fas fa-keyboard"></i> Interactive
                                </button>
                            </div>
                            <div class="btn-group me-2">
                                <button class="btn btn-outline-secondary btn-sm" onclick="resetCode()">
                                    <i class="fas fa-undo"></i> Reset
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="downloadCode()">
                                    <i class="fas fa-download"></i> Download
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="shareCode()">
                                    <i class="fas fa-share"></i> Share
                                </button>
                            </div>
                            <button class="btn btn-outline-dark btn-sm" onclick="toggleFullscreen()">
                                <i class="fas fa-expand"></i> Fullscreen
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Code Workspace -->
            <div class="code-workspace">
                <div class="code-panel">
                    <div class="panel-header">
                        <i class="fas fa-edit"></i> Code Editor
                    </div>
                    <div class="panel-content">
                        <div id="monaco-editor"></div>
                    </div>
                </div>
                
                <div class="output-panel">
                    <div class="panel-header">
                        <i class="fas fa-terminal"></i> Output & Results
                        <span class="ms-2" id="execution-time"></span>
                    </div>
                    <div class="panel-content">
                        <div class="output-content" id="output-content">
                            <div class="text-center text-muted">
                                <i class="fas fa-play-circle fa-2x mb-2"></i>
                                <p>Click <strong>Run</strong> to execute your code</p>
                                <p>Use <strong>Interactive</strong> for code with input() calls</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.34.0/min/vs/loader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let editor;
        let isFullscreen = false;
        const token = localStorage.getItem('access_token');
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            checkAuthentication();
            initializeEditor();
        });
        
        function checkAuthentication() {
            if (!token) {
                // Show login required message
                document.getElementById('output-content').innerHTML = `
                    <div class="alert alert-warning text-center">
                        <i class="fas fa-lock fa-2x mb-3"></i>
                        <h5>Authentication Required</h5>
                        <p>Please login to use the code editor.</p>
                        <a href="/" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt"></i> Go to Login
                        </a>
                    </div>
                `;
                
                // Disable editor functionality
                disableEditor();
                return false;
            }
            
            // Validate token
            fetch('/api/dashboard-stats', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            })
            .then(response => {
                if (response.ok) {
                    // Token valid, show authenticated navigation
                    document.getElementById('home-link').style.display = 'none';
                    document.getElementById('dashboard-link').style.display = 'block';
                    document.getElementById('logout-link').style.display = 'block';
                    
                    // Update dashboard link based on role
                    const role = localStorage.getItem('user_role') || 'student';
                    document.getElementById('dashboard-link').href = `/dashboard/${role}`;
                } else {
                    // Token invalid
                    localStorage.removeItem('access_token');
                    localStorage.removeItem('user_role');
                    checkAuthentication(); // Recursive call to show login message
                }
            })
            .catch(error => {
                console.error('Token validation error:', error);
            });
            
            return true;
        }
        
        function disableEditor() {
            // Disable all buttons
            document.querySelectorAll('button').forEach(btn => {
                if (!btn.onclick || !btn.onclick.toString().includes('logout')) {
                    btn.disabled = true;
                }
            });
            
            // Disable select
            document.getElementById('language-select').disabled = true;
        }
        
        function initializeEditor() {
            require.config({ paths: { vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.34.0/min/vs' } });
            
            require(['vs/editor/editor.main'], function () {
                editor = monaco.editor.create(document.getElementById('monaco-editor'), {
                    value: getStarterCode(),
                    language: 'python',
                    theme: 'vs-dark',
                    fontSize: 14,
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false,
                    automaticLayout: true,
                    wordWrap: 'on',
                    lineNumbers: 'on',
                    roundedSelection: false,
                    scrollbar: {
                        verticalScrollbarSize: 8,
                        horizontalScrollbarSize: 8
                    }
                });
                
                // Auto-save every 30 seconds
                setInterval(autoSave, 30000);
            });
        }
        
        function getStarterCode() {
            return `# Welcome to CS466 Code Editor
# Similar to online-python.com with enhanced features

def main():
    # Write your Python code here
    print("Hello, World!")
    
    # Example with input (use Interactive mode)
    # name = input("Enter your name: ")
    # print(f"Hello, {name}!")

if __name__ == "__main__":
    main()`;
        }
        
        async function runCode() {
            if (!editor) return;
            
            // Check authentication first
            if (!token) {
                showToast('Please login to run code!', 'warning');
                return;
            }
            
            const code = editor.getValue();
            const language = document.getElementById('language-select').value;
            const outputDiv = document.getElementById('output-content');
            
            if (!code.trim()) {
                outputDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> Please enter code before running!</div>';
                return;
            }
            
            // Check if code has input() calls
            if (code.includes('input(') && language === 'python') {
                outputDiv.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> 
                        <strong>Interactive Code Detected!</strong><br>
                        Your code contains input() calls. Please use the <strong>Interactive</strong> button for better experience.
                        <br><br>
                        <button class="btn btn-primary btn-sm" onclick="runInteractiveMode()">
                            <i class="fas fa-keyboard"></i> Switch to Interactive Mode
                        </button>
                    </div>
                `;
                return;
            }
            
            const startTime = Date.now();
            outputDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><br><br>Running code...</div>';
            
            try {
                const formData = new FormData();
                formData.append('code', code);
                formData.append('language', language);
                
                const response = await fetch('/code/run', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });
                
                const result = await response.json();
                const endTime = Date.now();
                const executionTime = endTime - startTime;
                
                document.getElementById('execution-time').innerHTML = `<small class="text-muted">Execution: ${executionTime}ms</small>`;
                
                if (result.success) {
                    displayOutput(result.result, executionTime);
                } else {
                    outputDiv.innerHTML = `<div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> 
                        <strong>Error:</strong> ${result.result?.error || 'Unknown error'}
                    </div>`;
                }
            } catch (error) {
                outputDiv.innerHTML = `<div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> 
                    <strong>Connection Error:</strong> ${error.message}
                </div>`;
            }
        }
        
        function displayOutput(result, executionTime) {
            const outputDiv = document.getElementById('output-content');
            let outputHtml = '';
            
            if (result.output) {
                outputHtml += `
                    <div class="mb-3">
                        <h6><i class="fas fa-terminal text-success"></i> Output:</h6>
                        <div class="code-output">
                            <pre>${escapeHtml(result.output)}</pre>
                        </div>
                    </div>
                `;
            }
            
            if (result.error) {
                outputHtml += `
                    <div class="mb-3">
                        <h6><i class="fas fa-exclamation-triangle text-danger"></i> Error:</h6>
                        <div class="code-output" style="background: #f8d7da; border-color: #f5c6cb;">
                            <pre>${escapeHtml(result.error)}</pre>
                        </div>
                    </div>
                `;
            }
            
            if (!result.output && !result.error) {
                outputHtml += '<div class="text-muted"><em>No output produced</em></div>';
            }
            
            outputDiv.innerHTML = outputHtml;
        }
        
        function runInteractiveMode() {
            if (!editor) return;
            
            // Check authentication first
            if (!token) {
                showToast('Please login to run interactive code!', 'warning');
                return;
            }
            
            const code = editor.getValue();
            if (!code.trim()) {
                showToast('Please enter code before running!', 'warning');
                return;
            }
            
            // Check if code has input() calls
            if (code.includes('input(')) {
                showInteractiveDialog();
            } else {
                // Regular execution
                runCode();
            }
        }
        
        function showInteractiveDialog() {
            const dialogHTML = `
                <div class="modal fade" id="interactiveModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-keyboard"></i> Interactive Mode
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> 
                                    Your code contains input() calls. Please provide the input values below.
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Input Values (one per line):</label>
                                    <textarea class="form-control" id="inputValues" rows="6" 
                                              placeholder="Enter each input value on a new line&#10;&#10;Example:&#10;John Doe&#10;25&#10;Python Developer"></textarea>
                                </div>
                                <div class="alert alert-warning">
                                    <i class="fas fa-lightbulb"></i> 
                                    <strong>Tip:</strong> Each line will be used as an input value when your code calls input()
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-success" onclick="executeInteractiveCode()">
                                    <i class="fas fa-play"></i> Run Interactive
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Add modal to DOM
            const existingModal = document.getElementById('interactiveModal');
            if (existingModal) existingModal.remove();
            
            document.body.insertAdjacentHTML('beforeend', dialogHTML);
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('interactiveModal'));
            modal.show();
        }
        
        async function executeInteractiveCode() {
            const code = editor.getValue();
            const inputValues = document.getElementById('inputValues').value.trim();
            const inputs = inputValues ? inputValues.split('\n') : [];
            const outputDiv = document.getElementById('output-content');
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('interactiveModal'));
            modal.hide();
            
            const startTime = Date.now();
            outputDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><br><br>Executing interactive code...</div>';
            
            try {
                const formData = new FormData();
                formData.append('code', code);
                formData.append('inputs', JSON.stringify(inputs));
                
                const response = await fetch('/code/run-interactive', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });
                
                const result = await response.json();
                const endTime = Date.now();
                const executionTime = endTime - startTime;
                
                document.getElementById('execution-time').innerHTML = `<small class="text-muted">Interactive: ${executionTime}ms</small>`;
                
                if (result.success) {
                    displayInteractiveOutput(result.result, executionTime);
                } else {
                    outputDiv.innerHTML = `<div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> 
                        <strong>Error:</strong> ${result.result?.error || 'Unknown error'}
                    </div>`;
                }
                
            } catch (error) {
                outputDiv.innerHTML = `<div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> 
                    <strong>Network Error:</strong> ${error.message}
                </div>`;
            }
        }
        
        function displayInteractiveOutput(result, executionTime) {
            const outputDiv = document.getElementById('output-content');
            let outputHtml = '';
            
            if (result.output) {
                outputHtml += `
                    <div class="mb-3">
                        <h6><i class="fas fa-terminal text-success"></i> Interactive Output:</h6>
                        <div class="code-output">
                            <pre>${escapeHtml(result.output)}</pre>
                        </div>
                    </div>
                `;
            }
            
            if (result.error) {
                outputHtml += `
                    <div class="mb-3">
                        <h6><i class="fas fa-exclamation-triangle text-danger"></i> Error:</h6>
                        <div class="code-output" style="background: #f8d7da; border-color: #f5c6cb;">
                            <pre>${escapeHtml(result.error)}</pre>
                        </div>
                    </div>
                `;
            }
            
            if (result.needs_input) {
                outputHtml += `<div class="alert alert-warning">
                    <i class="fas fa-info-circle"></i> 
                    Program requires more input. Please provide additional values and try again.
                </div>`;
            }
            
            if (!result.output && !result.error) {
                outputHtml += '<div class="text-muted"><em>No output produced</em></div>';
            }
            
            outputDiv.innerHTML = outputHtml;
        }
        
        function resetCode() {
            if (editor) {
                editor.setValue(getStarterCode());
                document.getElementById('output-content').innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-play-circle fa-2x mb-2"></i>
                        <p>Click <strong>Run</strong> to execute your code</p>
                        <p>Use <strong>Interactive</strong> for code with input() calls</p>
                    </div>
                `;
                document.getElementById('execution-time').innerHTML = '';
            }
        }
        
        function downloadCode() {
            if (!editor) return;
            
            const code = editor.getValue();
            const language = document.getElementById('language-select').value;
            
            if (!code.trim()) {
                showToast('No code to download!', 'warning');
                return;
            }
            
            const blob = new Blob([code], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `code_${Date.now()}.${language === 'python' ? 'py' : 'pl'}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showToast('Code downloaded successfully!', 'success');
        }
        
        function shareCode() {
            if (!editor) return;
            
            const code = editor.getValue();
            const language = document.getElementById('language-select').value;
            
            if (!code.trim()) {
                showToast('No code to share!', 'warning');
                return;
            }
            
            // Create share URL
            const shareData = {
                title: 'CS466 Code Share',
                code: code,
                language: language,
                timestamp: new Date().toISOString()
            };
            
            const shareURL = `${window.location.origin}/shared/${btoa(JSON.stringify(shareData))}`;
            
            // Copy to clipboard
            navigator.clipboard.writeText(shareURL).then(() => {
                showToast('Share URL copied to clipboard!', 'success');
            }).catch(() => {
                // Fallback for older browsers
                const textarea = document.createElement('textarea');
                textarea.value = shareURL;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                showToast('Share URL copied to clipboard!', 'success');
            });
        }
        
        function toggleFullscreen() {
            const container = document.querySelector('.main-container');
            const card = document.querySelector('.editor-card');
            
            if (!isFullscreen) {
                container.style.position = 'fixed';
                container.style.top = '0';
                container.style.left = '0';
                container.style.width = '100%';
                container.style.height = '100%';
                container.style.zIndex = '9999';
                container.style.margin = '0';
                container.style.padding = '0';
                card.style.height = '100%';
                card.style.borderRadius = '0';
                
                document.querySelector('.code-workspace').style.height = 'calc(100vh - 140px)';
                isFullscreen = true;
                
                document.querySelector('[onclick="toggleFullscreen()"] i').className = 'fas fa-compress';
            } else {
                container.style.position = 'relative';
                container.style.top = 'auto';
                container.style.left = 'auto';
                container.style.width = 'auto';
                container.style.height = 'auto';
                container.style.zIndex = 'auto';
                container.style.margin = '20px auto';
                container.style.padding = '0 15px';
                card.style.height = 'auto';
                card.style.borderRadius = 'var(--border-radius)';
                
                document.querySelector('.code-workspace').style.height = '600px';
                isFullscreen = false;
                
                document.querySelector('[onclick="toggleFullscreen()"] i').className = 'fas fa-expand';
            }
            
            // Trigger editor resize
            setTimeout(() => {
                if (editor) {
                    editor.layout();
                }
            }, 100);
        }
        
        function autoSave() {
            if (editor) {
                localStorage.setItem('code_editor_content', editor.getValue());
                localStorage.setItem('code_editor_language', document.getElementById('language-select').value);
            }
        }
        
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function logout() {
            localStorage.removeItem('access_token');
            localStorage.removeItem('user_role');
            window.location.href = '/';
        }
        
        // Load saved code on page load
        window.addEventListener('load', function() {
            const savedCode = localStorage.getItem('code_editor_content');
            const savedLanguage = localStorage.getItem('code_editor_language');
            
            if (savedCode && editor) {
                editor.setValue(savedCode);
            }
            
            if (savedLanguage) {
                document.getElementById('language-select').value = savedLanguage;
            }
        });
        
        // Language change handler
        document.getElementById('language-select').addEventListener('change', function() {
            const language = this.value;
            if (editor) {
                monaco.editor.setModelLanguage(editor.getModel(), language === 'python' ? 'python' : 'perl');
            }
        });
    </script>
</body>
</html> 